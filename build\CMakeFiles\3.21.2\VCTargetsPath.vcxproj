<?xml version="1.0" encoding="UTF-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
	<ItemGroup Label="ProjectConfigurations">
		<ProjectConfiguration Include="Debug|x64">
			<Configuration>Debug</Configuration>
			<Platform>x64</Platform>
		</ProjectConfiguration>
	</ItemGroup>
	<PropertyGroup Label="Globals">
		<ProjectGuid>{F3FC6D86-508D-3FB1-96D2-995F08B142EC}</ProjectGuid>
		<Keyword>Win32Proj</Keyword>
		<Platform>x64</Platform>
		<WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
	</PropertyGroup>
	<Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
	<PropertyGroup>
		<PreferredToolArchitecture>x64</PreferredToolArchitecture>
	</PropertyGroup>
	<PropertyGroup Label="Configuration">
		<ConfigurationType>Utility</ConfigurationType>
		<CharacterSet>MultiByte</CharacterSet>
		<PlatformToolset>v142</PlatformToolset>
	</PropertyGroup>
	<Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
	<ItemDefinitionGroup>
		<PostBuildEvent>
			<Command>echo VCTargetsPath=$(VCTargetsPath)</Command>
		</PostBuildEvent>
	</ItemDefinitionGroup>
	<Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
</Project>
