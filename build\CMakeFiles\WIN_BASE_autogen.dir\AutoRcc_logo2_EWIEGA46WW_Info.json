{"BUILD_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen", "CMAKE_BINARY_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build", "CMAKE_CURRENT_BINARY_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build", "CMAKE_CURRENT_SOURCE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "CMAKE_SOURCE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "INCLUDE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include", "INCLUDE_DIR_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_Release", "INPUTS": ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/quick.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/read.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/3.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/upLoad1.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/shot3.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/download.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/email2.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/2.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/write.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/shot2.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/running.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/database.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/1.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/revert.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/record3.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/scan.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/greymap2.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/upLoad.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/record2.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/share.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/slow.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/fit2.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/revertAll.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/download2.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/home.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/lidar.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/insert.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/greymap.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/reveret1.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/list.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/insertImage.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/import.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/yumao.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/scan5.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/email.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/magnifier.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/sacn.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/refresh.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/fit.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/delete.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/histogram.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/sacn4.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/calibration3.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/scan3.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/shot.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/searchOnce.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/cat.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/record.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/copy.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/calibration.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/serachAll.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/calibration2.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/upLoad2.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/logo/stop.png"], "LOCK_FILE": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": true, "OPTIONS": ["-name", "logo2"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_logo2.cpp", "RCC_EXECUTABLE": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Used.txt", "SETTINGS_FILE_Debug": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Used_Debug.txt", "SETTINGS_FILE_MinSizeRel": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Used_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Used_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Used_Release.txt", "SOURCE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/logo2.qrc", "VERBOSITY": 0}