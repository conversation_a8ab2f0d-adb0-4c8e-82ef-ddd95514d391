﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3A64A487-E8B2-3202-8B19-F249F4C60E24}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\a3803b1d4ba95fe801690c78413ac5c1\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCXXInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCommonLanguageInclude.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeGenericSystem.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeInitializeConfigs.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeLanguageInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeParseArguments.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeRCInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInitialize.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\WindowsPaths.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeCXXCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeRCCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeSystem.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\generate.stamp;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\CMakeFiles\generate.stamp;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\customPlot\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCXXInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCommonLanguageInclude.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeGenericSystem.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeInitializeConfigs.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeLanguageInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeParseArguments.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeRCInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInitialize.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\WindowsPaths.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeCXXCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeRCCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeSystem.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\generate.stamp;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\CMakeFiles\generate.stamp;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\customPlot\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCXXInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCommonLanguageInclude.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeGenericSystem.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeInitializeConfigs.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeLanguageInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeParseArguments.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeRCInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInitialize.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\WindowsPaths.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeCXXCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeRCCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeSystem.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\generate.stamp;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\CMakeFiles\generate.stamp;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\customPlot\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCXXInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCommonLanguageInclude.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeGenericSystem.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeInitializeConfigs.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeLanguageInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeParseArguments.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeRCInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInitialize.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\WindowsPaths.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeCXXCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeRCCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeSystem.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\CMakeLists.txt;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\generate.stamp;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\CMakeFiles\generate.stamp;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\customPlot\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>