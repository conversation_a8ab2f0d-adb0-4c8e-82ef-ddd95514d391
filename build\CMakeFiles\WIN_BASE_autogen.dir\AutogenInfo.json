{"BUILD_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen", "CMAKE_BINARY_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build", "CMAKE_CURRENT_BINARY_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build", "CMAKE_CURRENT_SOURCE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "CMAKE_EXECUTABLE": "D:/Programs/CMake/bin/cmake.exe", "CMAKE_LIST_FILES": ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/CMakeLists.txt", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/3.21.2/CMakeSystem.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeSystemSpecificInitialize.cmake", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/3.21.2/CMakeCXXCompiler.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeSystemSpecificInformation.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeGenericSystem.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeInitializeConfigs.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/Windows.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/WindowsPaths.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeCXXInformation.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeLanguageInformation.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/MSVC-CXX.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/Windows-MSVC-CXX.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/Windows-MSVC.cmake", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/3.21.2/CMakeRCCompiler.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeRCInformation.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeCommonLanguageInclude.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeParseArguments.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeParseArguments.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5AxContainer/Qt5AxContainerConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5AxContainer/Qt5AxContainerConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5AxBase/Qt5AxBaseConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5AxBase/Qt5AxBaseConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Sql/Qt5SqlConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Sql/Qt5SqlConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Sql/Qt5Sql_QODBCDriverPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Sql/Qt5Sql_QPSQLDriverPlugin.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Sql/Qt5Sql_QSQLiteDriverPlugin.cmake", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/logo.qrc", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/logo2.qrc", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/res.qrc"], "CMAKE_SOURCE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["F:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/winbase_app_0908/calibration_chart.h", "MU", "EWIEGA46WW/moc_calibration_chart.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/dtof_calibration.h", "MU", "EWIEGA46WW/moc_dtof_calibration.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/dynamic_calibration.h", "MU", "EWIEGA46WW/moc_dynamic_calibration.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/winbase_app_0908/greymap_chart.h", "MU", "EWIEGA46WW/moc_greymap_chart.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/winbase_app_0908/histogram_chart.h", "MU", "EWIEGA46WW/moc_histogram_chart.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/import_pointcloud.h", "MU", "EWIEGA46WW/moc_import_pointcloud.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/lidar_database.h", "MU", "EWIEGA46WW/moc_lidar_database.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/lidar_protocol.h", "MU", "EWIEGA46WW/moc_lidar_protocol.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/other_protocol.h", "MU", "EWIEGA46WW/moc_other_protocol.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/pointcloud_quality.h", "MU", "EWIEGA46WW/moc_pointcloud_quality.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/winbase_app_0908/pointclouds_chart.h", "MU", "EWIEGA46WW/moc_pointclouds_chart.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/record_pointcloud.h", "MU", "EWIEGA46WW/moc_record_pointcloud.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/saveas_capture.h", "MU", "EWIEGA46WW/moc_saveas_capture.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/serial_base.h", "MU", "EWIEGA46WW/moc_serial_base.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/show_image.h", "MU", "EWIEGA46WW/moc_show_image.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/task_process/task_base.h", "MU", "FLNDMKDV5U/moc_task_base.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/winbase_app_0908/widget.h", "MU", "EWIEGA46WW/moc_widget.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include", "INCLUDE_DIR_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_Release", "MOC_COMPILATION_FILE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": ["APP_VERSION=\"2.3.11\"", "QT_AXBASE_LIB", "QT_AXCONTAINER_LIB", "QT_CORE_LIB", "QT_DEPRECATED_WARNINGS", "QT_GUI_LIB", "QT_MESSAGELOGCONTEXT", "QT_NO_DEBUG", "QT_PRINTSUPPORT_LIB", "QT_SERIALPORT_LIB", "QT_SQL_LIB", "QT_WIDGETS_LIB", "USING_CUSTOM_HIDE=0", "USING_CUSTOM_STYLE=0", "USING_LOG_TO_FILE=0", "WIN32"], "MOC_DEFINITIONS_Debug": ["APP_VERSION=\"2.3.11\"", "QT_AXBASE_LIB", "QT_AXCONTAINER_LIB", "QT_CORE_LIB", "QT_DEPRECATED_WARNINGS", "QT_GUI_LIB", "QT_MESSAGELOGCONTEXT", "QT_PRINTSUPPORT_LIB", "QT_SERIALPORT_LIB", "QT_SQL_LIB", "QT_WIDGETS_LIB", "USING_CUSTOM_HIDE=0", "USING_CUSTOM_STYLE=0", "USING_LOG_TO_FILE=0", "WIN32"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/polynomialFit", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot", "F:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/winbase_app_0908/eigen-3.4.0", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/dll", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtCore", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/mkspecs/win32-g++", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtGui", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtANGLE", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtWidgets", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtSerialPort", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtPrintSupport", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/ActiveQt", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtSql"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": true, "PARALLEL": 4, "PARSE_CACHE_FILE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 14, "SETTINGS_FILE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["F:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/winbase_app_0908/calibration_chart.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/dynamic_calibration.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/greymap_chart.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/winbase_app_0908/histogram_chart.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/import_pointcloud.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/lidar_database.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/lidar_protocol.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/main.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/other_protocol.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/pointcloud_quality.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/winbase_app_0908/pointclouds_chart.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/record_pointcloud.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/saveas_capture.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/serial_base.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/show_image.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/task_process/task_base.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/widget.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "VERBOSITY": 0}