{"BUILD_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen", "CMAKE_BINARY_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build", "CMAKE_CURRENT_BINARY_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build", "CMAKE_CURRENT_SOURCE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "CMAKE_SOURCE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "INCLUDE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include", "INCLUDE_DIR_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_Release", "INPUTS": ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/stop.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/running.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/record.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/calibration.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/histogram.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/greymap.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/shot.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/running2.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/import.jpeg", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/save.jpeg", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/scan.png", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/stop2.png"], "LOCK_FILE": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": true, "OPTIONS": ["-name", "logo"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_logo.cpp", "RCC_EXECUTABLE": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Used.txt", "SETTINGS_FILE_Debug": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Used_Debug.txt", "SETTINGS_FILE_MinSizeRel": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Used_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Used_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Used_Release.txt", "SOURCE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/logo.qrc", "VERBOSITY": 0}