{
    "cmake.buildDirectory": "${workspaceFolder}/build/",
    "cmake.configureOnOpen": true,
    "cmake.generator": "Ninja",
    "cmake.configureArgs": [
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
    ],
    "cmake.configureSettings": {
        "CMAKE_BUILD_TYPE": "${buildType}",
        "CMAKE_PREFIX_PATH": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64",
        "CMAKE_C_COMPILER": "D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/gcc.exe",
        "CMAKE_CXX_COMPILER": "D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/g++.exe"
    },
    "python.terminal.activateEnvironment": true,
    "python.terminal.activateEnvInCurrentTerminal": true, // 可选，但推荐
    "files.associations": {
        "qdialog": "cpp",
        "*.tcc": "cpp",
        "deque": "cpp",
        "list": "cpp",
        "string": "cpp",
        "unordered_map": "cpp",
        "vector": "cpp",
        "hash_map": "cpp",
        "random": "cpp",
        "new": "cpp",
        "geometry": "cpp",
        "lu": "cpp",
        "sparseqr": "cpp",
        "specialfunctions": "cpp",
        "algorithm": "cpp",
        "array": "cpp",
        "atomic": "cpp",
        "cassert": "cpp",
        "ccomplex": "cpp",
        "cctype": "cpp",
        "cerrno": "cpp",
        "cfloat": "cpp",
        "chrono": "cpp",
        "climits": "cpp",
        "clocale": "cpp",
        "cmath": "cpp",
        "complex": "cpp",
        "condition_variable": "cpp",
        "cstdarg": "cpp",
        "cstddef": "cpp",
        "cstdint": "cpp",
        "cstdio": "cpp",
        "cstdlib": "cpp",
        "cstring": "cpp",
        "ctime": "cpp",
        "cwchar": "cpp",
        "cwctype": "cpp",
        "map": "cpp",
        "set": "cpp",
        "exception": "cpp",
        "functional": "cpp",
        "iterator": "cpp",
        "memory": "cpp",
        "memory_resource": "cpp",
        "numeric": "cpp",
        "optional": "cpp",
        "ratio": "cpp",
        "string_view": "cpp",
        "system_error": "cpp",
        "tuple": "cpp",
        "type_traits": "cpp",
        "utility": "cpp",
        "fstream": "cpp",
        "future": "cpp",
        "initializer_list": "cpp",
        "iomanip": "cpp",
        "ios": "cpp",
        "iosfwd": "cpp",
        "iostream": "cpp",
        "istream": "cpp",
        "limits": "cpp",
        "locale": "cpp",
        "mutex": "cpp",
        "ostream": "cpp",
        "queue": "cpp",
        "sstream": "cpp",
        "stdexcept": "cpp",
        "streambuf": "cpp",
        "thread": "cpp",
        "cinttypes": "cpp",
        "typeinfo": "cpp",
        "variant": "cpp",
        "qapplication": "cpp",
        "qobject": "cpp",
        "qtimer": "cpp",
        "qstandardpaths": "cpp",
        "qmetaenum": "cpp",
        "qpushbutton": "cpp",
        "qsettings": "cpp",
        "qdebug": "cpp",
        "qsqlrecord": "cpp",
        "qmessagebox": "cpp",
        "qtimerevent": "cpp",
        "fft": "cpp",
        "qitemselectionmodel": "cpp",
        "qmenu": "cpp",
        "qregularexpression": "cpp",
        "qregularexpressionmatchiterator": "cpp",
        "qsplitter": "cpp",
        "qthread": "cpp",
        "qscrollbar": "cpp",
        "qshortcut": "cpp",
        "qwheelevent": "cpp",
        "qregularexpressionmatch": "cpp",
        "qcombobox": "cpp",
        "typeindex": "cpp",
        "qstringlistmodel": "cpp",
        "qheaderview": "cpp",
        "mprealsupport": "cpp",
        "tensor": "cpp",
        "qstring": "cpp"
    }
}