/****************************************************************************
** Meta object code from reading C++ file 'lidar_database.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../lidar_database.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'lidar_database.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_LidarDatabase_t {
    QByteArrayData data[42];
    char stringdata0[659];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_LidarDatabase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_LidarDatabase_t qt_meta_stringdata_LidarDatabase = {
    {
QT_MOC_LITERAL(0, 0, 13), // "LidarDatabase"
QT_MOC_LITERAL(1, 14, 17), // "TransmitDataLidar"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 3), // "str"
QT_MOC_LITERAL(4, 37, 13), // "TransmitImage"
QT_MOC_LITERAL(5, 51, 3), // "map"
QT_MOC_LITERAL(6, 55, 1), // "w"
QT_MOC_LITERAL(7, 57, 1), // "h"
QT_MOC_LITERAL(8, 59, 17), // "TransmitZeroAngle"
QT_MOC_LITERAL(9, 77, 12), // "feedbackType"
QT_MOC_LITERAL(10, 90, 4), // "data"
QT_MOC_LITERAL(11, 95, 5), // "angle"
QT_MOC_LITERAL(12, 101, 16), // "TransmitHistType"
QT_MOC_LITERAL(13, 118, 7), // "uint8_t"
QT_MOC_LITERAL(14, 126, 4), // "type"
QT_MOC_LITERAL(15, 131, 15), // "FeedbackInfoTmp"
QT_MOC_LITERAL(16, 147, 3), // "fdb"
QT_MOC_LITERAL(17, 151, 17), // "FeedbackInfoLidar"
QT_MOC_LITERAL(18, 169, 23), // "ReceviceCalibrationData"
QT_MOC_LITERAL(19, 193, 18), // "std::vector<float>"
QT_MOC_LITERAL(20, 212, 1), // "p"
QT_MOC_LITERAL(21, 214, 11), // "sig_iap_ack"
QT_MOC_LITERAL(22, 226, 9), // "iap_ack_t"
QT_MOC_LITERAL(23, 236, 3), // "ack"
QT_MOC_LITERAL(24, 240, 29), // "on_pushButtonTransmit_clicked"
QT_MOC_LITERAL(25, 270, 13), // "IdSelectSlots"
QT_MOC_LITERAL(26, 284, 14), // "CmdSelectSlots"
QT_MOC_LITERAL(27, 299, 7), // "timeOut"
QT_MOC_LITERAL(28, 307, 6), // "ReShow"
QT_MOC_LITERAL(29, 314, 27), // "on_actionDSchOnce_triggered"
QT_MOC_LITERAL(30, 342, 26), // "on_actionDSchAll_triggered"
QT_MOC_LITERAL(31, 369, 26), // "on_actionDSubmit_triggered"
QT_MOC_LITERAL(32, 396, 29), // "on_actionDSubmitAll_triggered"
QT_MOC_LITERAL(33, 426, 26), // "on_actionDInsert_triggered"
QT_MOC_LITERAL(34, 453, 26), // "on_actionDIstImg_triggered"
QT_MOC_LITERAL(35, 480, 26), // "on_actionDRevert_triggered"
QT_MOC_LITERAL(36, 507, 29), // "on_actionDRevertAll_triggered"
QT_MOC_LITERAL(37, 537, 24), // "on_actionLCopy_triggered"
QT_MOC_LITERAL(38, 562, 29), // "on_actionDShowIamge_triggered"
QT_MOC_LITERAL(39, 592, 38), // "on_comboBoxRunningMode_editTe..."
QT_MOC_LITERAL(40, 631, 4), // "arg1"
QT_MOC_LITERAL(41, 636, 22) // "on_actionOTA_triggered"

    },
    "LidarDatabase\0TransmitDataLidar\0\0str\0"
    "TransmitImage\0map\0w\0h\0TransmitZeroAngle\0"
    "feedbackType\0data\0angle\0TransmitHistType\0"
    "uint8_t\0type\0FeedbackInfoTmp\0fdb\0"
    "FeedbackInfoLidar\0ReceviceCalibrationData\0"
    "std::vector<float>\0p\0sig_iap_ack\0"
    "iap_ack_t\0ack\0on_pushButtonTransmit_clicked\0"
    "IdSelectSlots\0CmdSelectSlots\0timeOut\0"
    "ReShow\0on_actionDSchOnce_triggered\0"
    "on_actionDSchAll_triggered\0"
    "on_actionDSubmit_triggered\0"
    "on_actionDSubmitAll_triggered\0"
    "on_actionDInsert_triggered\0"
    "on_actionDIstImg_triggered\0"
    "on_actionDRevert_triggered\0"
    "on_actionDRevertAll_triggered\0"
    "on_actionLCopy_triggered\0"
    "on_actionDShowIamge_triggered\0"
    "on_comboBoxRunningMode_editTextChanged\0"
    "arg1\0on_actionOTA_triggered"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_LidarDatabase[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      25,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,  139,    2, 0x06 /* Public */,
       4,    3,  142,    2, 0x06 /* Public */,
       8,    3,  149,    2, 0x06 /* Public */,
      12,    1,  156,    2, 0x06 /* Public */,
      15,    1,  159,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      17,    1,  162,    2, 0x0a /* Public */,
      18,    1,  165,    2, 0x0a /* Public */,
      21,    1,  168,    2, 0x0a /* Public */,
      24,    0,  171,    2, 0x08 /* Private */,
      25,    0,  172,    2, 0x08 /* Private */,
      26,    0,  173,    2, 0x08 /* Private */,
      27,    0,  174,    2, 0x08 /* Private */,
      28,    0,  175,    2, 0x08 /* Private */,
      29,    0,  176,    2, 0x08 /* Private */,
      30,    0,  177,    2, 0x08 /* Private */,
      31,    0,  178,    2, 0x08 /* Private */,
      32,    0,  179,    2, 0x08 /* Private */,
      33,    0,  180,    2, 0x08 /* Private */,
      34,    0,  181,    2, 0x08 /* Private */,
      35,    0,  182,    2, 0x08 /* Private */,
      36,    0,  183,    2, 0x08 /* Private */,
      37,    0,  184,    2, 0x08 /* Private */,
      38,    0,  185,    2, 0x08 /* Private */,
      39,    1,  186,    2, 0x08 /* Private */,
      41,    0,  189,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QByteArray,    3,
    QMetaType::Void, QMetaType::QByteArray, QMetaType::Int, QMetaType::Int,    5,    6,    7,
    QMetaType::Void, QMetaType::Int, QMetaType::QByteArray, QMetaType::Float,    9,   10,   11,
    QMetaType::Void, 0x80000000 | 13,   14,
    QMetaType::Void, QMetaType::QByteArray,   16,

 // slots: parameters
    QMetaType::Void, QMetaType::QByteArray,   16,
    QMetaType::Void, 0x80000000 | 19,   20,
    QMetaType::Void, 0x80000000 | 22,   23,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   40,
    QMetaType::Void,

       0        // eod
};

void LidarDatabase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<LidarDatabase *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->TransmitDataLidar((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 1: _t->TransmitImage((*reinterpret_cast< QByteArray(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 2: _t->TransmitZeroAngle((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< QByteArray(*)>(_a[2])),(*reinterpret_cast< float(*)>(_a[3]))); break;
        case 3: _t->TransmitHistType((*reinterpret_cast< uint8_t(*)>(_a[1]))); break;
        case 4: _t->FeedbackInfoTmp((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 5: _t->FeedbackInfoLidar((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 6: _t->ReceviceCalibrationData((*reinterpret_cast< std::vector<float>(*)>(_a[1]))); break;
        case 7: _t->sig_iap_ack((*reinterpret_cast< iap_ack_t(*)>(_a[1]))); break;
        case 8: _t->on_pushButtonTransmit_clicked(); break;
        case 9: _t->IdSelectSlots(); break;
        case 10: _t->CmdSelectSlots(); break;
        case 11: _t->timeOut(); break;
        case 12: _t->ReShow(); break;
        case 13: _t->on_actionDSchOnce_triggered(); break;
        case 14: _t->on_actionDSchAll_triggered(); break;
        case 15: _t->on_actionDSubmit_triggered(); break;
        case 16: _t->on_actionDSubmitAll_triggered(); break;
        case 17: _t->on_actionDInsert_triggered(); break;
        case 18: _t->on_actionDIstImg_triggered(); break;
        case 19: _t->on_actionDRevert_triggered(); break;
        case 20: _t->on_actionDRevertAll_triggered(); break;
        case 21: _t->on_actionLCopy_triggered(); break;
        case 22: _t->on_actionDShowIamge_triggered(); break;
        case 23: _t->on_comboBoxRunningMode_editTextChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 24: _t->on_actionOTA_triggered(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (LidarDatabase::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LidarDatabase::TransmitDataLidar)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (LidarDatabase::*)(QByteArray , int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LidarDatabase::TransmitImage)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (LidarDatabase::*)(int , QByteArray , float );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LidarDatabase::TransmitZeroAngle)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (LidarDatabase::*)(uint8_t );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LidarDatabase::TransmitHistType)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (LidarDatabase::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LidarDatabase::FeedbackInfoTmp)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject LidarDatabase::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_LidarDatabase.data,
    qt_meta_data_LidarDatabase,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *LidarDatabase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *LidarDatabase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_LidarDatabase.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int LidarDatabase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 25)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 25;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 25)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 25;
    }
    return _id;
}

// SIGNAL 0
void LidarDatabase::TransmitDataLidar(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void LidarDatabase::TransmitImage(QByteArray _t1, int _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void LidarDatabase::TransmitZeroAngle(int _t1, QByteArray _t2, float _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void LidarDatabase::TransmitHistType(uint8_t _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void LidarDatabase::FeedbackInfoTmp(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
