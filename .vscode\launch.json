{"version": "0.2.0", "configurations": [{"name": "Debug", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/CSPC_LA_function.exe", "args": [], "stopAtEntry": true, "cwd": "${workspaceFolder}/build/bin", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "CMake: Build Debug", "internalConsoleOptions": "openOnSessionStart", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\9f3abd0ad12742a7d50ae3cb3b9f8a76\\tonka3000.qtvsctools\\qt.natvis.xml"}]}