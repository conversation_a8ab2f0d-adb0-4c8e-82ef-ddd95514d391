/****************************************************************************
** Meta object code from reading C++ file 'dynamic_calibration.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../dynamic_calibration.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'dynamic_calibration.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_DynamicCalibration_t {
    QByteArrayData data[29];
    char stringdata0[576];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_DynamicCalibration_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_DynamicCalibration_t qt_meta_stringdata_DynamicCalibration = {
    {
QT_MOC_LITERAL(0, 0, 18), // "DynamicCalibration"
QT_MOC_LITERAL(1, 19, 17), // "TransmitSerialCmd"
QT_MOC_LITERAL(2, 37, 0), // ""
QT_MOC_LITERAL(3, 38, 4), // "data"
QT_MOC_LITERAL(4, 43, 23), // "TransmitCalibrationData"
QT_MOC_LITERAL(5, 67, 18), // "std::vector<float>"
QT_MOC_LITERAL(6, 86, 20), // "TransmitCompensation"
QT_MOC_LITERAL(7, 107, 5), // "isExe"
QT_MOC_LITERAL(8, 113, 11), // "samplePoint"
QT_MOC_LITERAL(9, 125, 19), // "TransmitSamplePoint"
QT_MOC_LITERAL(10, 145, 2), // "hr"
QT_MOC_LITERAL(11, 148, 2), // "wt"
QT_MOC_LITERAL(12, 151, 2), // "bk"
QT_MOC_LITERAL(13, 154, 16), // "std::vector<int>"
QT_MOC_LITERAL(14, 171, 3), // "dis"
QT_MOC_LITERAL(15, 175, 4), // "type"
QT_MOC_LITERAL(16, 180, 35), // "on_comboBoxMode_currentIndexC..."
QT_MOC_LITERAL(17, 216, 5), // "index"
QT_MOC_LITERAL(18, 222, 39), // "on_pushButtonDynamicCalibrati..."
QT_MOC_LITERAL(19, 262, 30), // "on_actionTriTransmit_triggered"
QT_MOC_LITERAL(20, 293, 27), // "on_actionTriClear_triggered"
QT_MOC_LITERAL(21, 321, 30), // "on_actionTofTransmit_triggered"
QT_MOC_LITERAL(22, 352, 27), // "on_actionTofClear_triggered"
QT_MOC_LITERAL(23, 380, 38), // "on_pushButtonStaticCalibratio..."
QT_MOC_LITERAL(24, 419, 28), // "on_actionImptParam_triggered"
QT_MOC_LITERAL(25, 448, 28), // "on_actionExptParam_triggered"
QT_MOC_LITERAL(26, 477, 36), // "on_actioncompensationAngle_tr..."
QT_MOC_LITERAL(27, 514, 30), // "on_actionSamplePoint_triggered"
QT_MOC_LITERAL(28, 545, 30) // "on_actionCompareData_triggered"

    },
    "DynamicCalibration\0TransmitSerialCmd\0"
    "\0data\0TransmitCalibrationData\0"
    "std::vector<float>\0TransmitCompensation\0"
    "isExe\0samplePoint\0TransmitSamplePoint\0"
    "hr\0wt\0bk\0std::vector<int>\0dis\0type\0"
    "on_comboBoxMode_currentIndexChanged\0"
    "index\0on_pushButtonDynamicCalibration_clicked\0"
    "on_actionTriTransmit_triggered\0"
    "on_actionTriClear_triggered\0"
    "on_actionTofTransmit_triggered\0"
    "on_actionTofClear_triggered\0"
    "on_pushButtonStaticCalibration_clicked\0"
    "on_actionImptParam_triggered\0"
    "on_actionExptParam_triggered\0"
    "on_actioncompensationAngle_triggered\0"
    "on_actionSamplePoint_triggered\0"
    "on_actionCompareData_triggered"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_DynamicCalibration[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      16,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   94,    2, 0x06 /* Public */,
       4,    1,   97,    2, 0x06 /* Public */,
       6,    2,  100,    2, 0x06 /* Public */,
       9,    5,  105,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      16,    1,  116,    2, 0x08 /* Private */,
      18,    0,  119,    2, 0x08 /* Private */,
      19,    0,  120,    2, 0x08 /* Private */,
      20,    0,  121,    2, 0x08 /* Private */,
      21,    0,  122,    2, 0x08 /* Private */,
      22,    0,  123,    2, 0x08 /* Private */,
      23,    0,  124,    2, 0x08 /* Private */,
      24,    0,  125,    2, 0x08 /* Private */,
      25,    0,  126,    2, 0x08 /* Private */,
      26,    0,  127,    2, 0x08 /* Private */,
      27,    0,  128,    2, 0x08 /* Private */,
      28,    0,  129,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QByteArray,    3,
    QMetaType::Void, 0x80000000 | 5,    3,
    QMetaType::Void, QMetaType::Bool, 0x80000000 | 5,    7,    8,
    QMetaType::Void, 0x80000000 | 5, 0x80000000 | 5, 0x80000000 | 5, 0x80000000 | 13, QMetaType::UInt,   10,   11,   12,   14,   15,

 // slots: parameters
    QMetaType::Void, QMetaType::Int,   17,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void DynamicCalibration::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DynamicCalibration *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->TransmitSerialCmd((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 1: _t->TransmitCalibrationData((*reinterpret_cast< std::vector<float>(*)>(_a[1]))); break;
        case 2: _t->TransmitCompensation((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< std::vector<float>(*)>(_a[2]))); break;
        case 3: _t->TransmitSamplePoint((*reinterpret_cast< std::vector<float>(*)>(_a[1])),(*reinterpret_cast< std::vector<float>(*)>(_a[2])),(*reinterpret_cast< std::vector<float>(*)>(_a[3])),(*reinterpret_cast< std::vector<int>(*)>(_a[4])),(*reinterpret_cast< uint(*)>(_a[5]))); break;
        case 4: _t->on_comboBoxMode_currentIndexChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 5: _t->on_pushButtonDynamicCalibration_clicked(); break;
        case 6: _t->on_actionTriTransmit_triggered(); break;
        case 7: _t->on_actionTriClear_triggered(); break;
        case 8: _t->on_actionTofTransmit_triggered(); break;
        case 9: _t->on_actionTofClear_triggered(); break;
        case 10: _t->on_pushButtonStaticCalibration_clicked(); break;
        case 11: _t->on_actionImptParam_triggered(); break;
        case 12: _t->on_actionExptParam_triggered(); break;
        case 13: _t->on_actioncompensationAngle_triggered(); break;
        case 14: _t->on_actionSamplePoint_triggered(); break;
        case 15: _t->on_actionCompareData_triggered(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (DynamicCalibration::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DynamicCalibration::TransmitSerialCmd)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (DynamicCalibration::*)(std::vector<float> );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DynamicCalibration::TransmitCalibrationData)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (DynamicCalibration::*)(bool , std::vector<float> );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DynamicCalibration::TransmitCompensation)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (DynamicCalibration::*)(std::vector<float> , std::vector<float> , std::vector<float> , std::vector<int> , uint );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DynamicCalibration::TransmitSamplePoint)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject DynamicCalibration::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_DynamicCalibration.data,
    qt_meta_data_DynamicCalibration,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *DynamicCalibration::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DynamicCalibration::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_DynamicCalibration.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int DynamicCalibration::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 16;
    }
    return _id;
}

// SIGNAL 0
void DynamicCalibration::TransmitSerialCmd(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void DynamicCalibration::TransmitCalibrationData(std::vector<float> _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void DynamicCalibration::TransmitCompensation(bool _t1, std::vector<float> _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void DynamicCalibration::TransmitSamplePoint(std::vector<float> _t1, std::vector<float> _t2, std::vector<float> _t3, std::vector<int> _t4, uint _t5)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t5))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
