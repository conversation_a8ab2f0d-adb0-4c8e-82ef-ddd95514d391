{"BUILD_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen", "CMAKE_BINARY_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build", "CMAKE_CURRENT_BINARY_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build", "CMAKE_CURRENT_SOURCE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "CMAKE_SOURCE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "INCLUDE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include", "INCLUDE_DIR_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/include_Release", "INPUTS": ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/cspc1.jpg", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/icon/cspc.jpg"], "LOCK_FILE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": true, "OPTIONS": ["-name", "res"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_res.cpp", "RCC_EXECUTABLE": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Used.txt", "SETTINGS_FILE_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_Debug.txt", "SETTINGS_FILE_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "F:/13_<PERSON><PERSON>-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_Release.txt", "SOURCE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/res.qrc", "VERBOSITY": 0}