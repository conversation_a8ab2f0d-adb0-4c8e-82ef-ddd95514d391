/****************************************************************************
** Meta object code from reading C++ file 'pointclouds_chart.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../pointclouds_chart.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'pointclouds_chart.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_PointCloudsChart_t {
    QByteArrayData data[34];
    char stringdata0[618];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_PointCloudsChart_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_PointCloudsChart_t qt_meta_stringdata_PointCloudsChart = {
    {
QT_MOC_LITERAL(0, 0, 16), // "PointCloudsChart"
QT_MOC_LITERAL(1, 17, 17), // "TransmitSerialCmd"
QT_MOC_LITERAL(2, 35, 0), // ""
QT_MOC_LITERAL(3, 36, 4), // "data"
QT_MOC_LITERAL(4, 41, 23), // "TransmitCalibrationData"
QT_MOC_LITERAL(5, 65, 18), // "std::vector<float>"
QT_MOC_LITERAL(6, 84, 17), // "TransmitLidarInfo"
QT_MOC_LITERAL(7, 102, 32), // "std::vector<std::vector<float> >"
QT_MOC_LITERAL(8, 135, 21), // "ReceivePointCloudData"
QT_MOC_LITERAL(9, 157, 25), // "std::vector<ProtocolData>"
QT_MOC_LITERAL(10, 183, 15), // "ReceiveFileData"
QT_MOC_LITERAL(11, 199, 18), // "RecShowFileDataCmd"
QT_MOC_LITERAL(12, 218, 10), // "isFileData"
QT_MOC_LITERAL(13, 229, 14), // "RecStartRecord"
QT_MOC_LITERAL(14, 244, 8), // "fileName"
QT_MOC_LITERAL(15, 253, 13), // "RecStopRecord"
QT_MOC_LITERAL(16, 267, 8), // "SetRange"
QT_MOC_LITERAL(17, 276, 8), // "QCPRange"
QT_MOC_LITERAL(18, 285, 5), // "range"
QT_MOC_LITERAL(19, 291, 17), // "PickPointCallBack"
QT_MOC_LITERAL(20, 309, 12), // "QMouseEvent*"
QT_MOC_LITERAL(21, 322, 5), // "event"
QT_MOC_LITERAL(22, 328, 15), // "SetAdminFuction"
QT_MOC_LITERAL(23, 344, 8), // "isEnable"
QT_MOC_LITERAL(24, 353, 25), // "on_actionSaveAs_triggered"
QT_MOC_LITERAL(25, 379, 30), // "on_actionImportCloud_triggered"
QT_MOC_LITERAL(26, 410, 29), // "on_actionScreenShot_triggered"
QT_MOC_LITERAL(27, 440, 25), // "on_actionRecord_triggered"
QT_MOC_LITERAL(28, 466, 26), // "on_actionStandBy_triggered"
QT_MOC_LITERAL(29, 493, 26), // "on_actionRunning_triggered"
QT_MOC_LITERAL(30, 520, 36), // "on_actionRecordCalibration_tr..."
QT_MOC_LITERAL(31, 557, 10), // "timerEvent"
QT_MOC_LITERAL(32, 568, 12), // "QTimerEvent*"
QT_MOC_LITERAL(33, 581, 36) // "on_actionPointcloudQuality_tr..."

    },
    "PointCloudsChart\0TransmitSerialCmd\0\0"
    "data\0TransmitCalibrationData\0"
    "std::vector<float>\0TransmitLidarInfo\0"
    "std::vector<std::vector<float> >\0"
    "ReceivePointCloudData\0std::vector<ProtocolData>\0"
    "ReceiveFileData\0RecShowFileDataCmd\0"
    "isFileData\0RecStartRecord\0fileName\0"
    "RecStopRecord\0SetRange\0QCPRange\0range\0"
    "PickPointCallBack\0QMouseEvent*\0event\0"
    "SetAdminFuction\0isEnable\0"
    "on_actionSaveAs_triggered\0"
    "on_actionImportCloud_triggered\0"
    "on_actionScreenShot_triggered\0"
    "on_actionRecord_triggered\0"
    "on_actionStandBy_triggered\0"
    "on_actionRunning_triggered\0"
    "on_actionRecordCalibration_triggered\0"
    "timerEvent\0QTimerEvent*\0"
    "on_actionPointcloudQuality_triggered"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_PointCloudsChart[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      20,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,  114,    2, 0x06 /* Public */,
       4,    1,  117,    2, 0x06 /* Public */,
       6,    1,  120,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    1,  123,    2, 0x0a /* Public */,
      10,    1,  126,    2, 0x0a /* Public */,
      11,    1,  129,    2, 0x0a /* Public */,
      13,    1,  132,    2, 0x0a /* Public */,
      15,    0,  135,    2, 0x0a /* Public */,
      16,    1,  136,    2, 0x0a /* Public */,
      19,    1,  139,    2, 0x0a /* Public */,
      22,    1,  142,    2, 0x0a /* Public */,
      24,    0,  145,    2, 0x08 /* Private */,
      25,    0,  146,    2, 0x08 /* Private */,
      26,    0,  147,    2, 0x08 /* Private */,
      27,    0,  148,    2, 0x08 /* Private */,
      28,    0,  149,    2, 0x08 /* Private */,
      29,    0,  150,    2, 0x08 /* Private */,
      30,    0,  151,    2, 0x08 /* Private */,
      31,    1,  152,    2, 0x08 /* Private */,
      33,    0,  155,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QByteArray,    3,
    QMetaType::Void, 0x80000000 | 5,    3,
    QMetaType::Void, 0x80000000 | 7,    3,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 9,    3,
    QMetaType::Void, 0x80000000 | 9,    3,
    QMetaType::Void, QMetaType::Bool,   12,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 17,   18,
    QMetaType::Void, 0x80000000 | 20,   21,
    QMetaType::Void, QMetaType::Bool,   23,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 32,   21,
    QMetaType::Void,

       0        // eod
};

void PointCloudsChart::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<PointCloudsChart *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->TransmitSerialCmd((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 1: _t->TransmitCalibrationData((*reinterpret_cast< std::vector<float>(*)>(_a[1]))); break;
        case 2: _t->TransmitLidarInfo((*reinterpret_cast< std::vector<std::vector<float> >(*)>(_a[1]))); break;
        case 3: _t->ReceivePointCloudData((*reinterpret_cast< std::vector<ProtocolData>(*)>(_a[1]))); break;
        case 4: _t->ReceiveFileData((*reinterpret_cast< std::vector<ProtocolData>(*)>(_a[1]))); break;
        case 5: _t->RecShowFileDataCmd((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 6: _t->RecStartRecord((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 7: _t->RecStopRecord(); break;
        case 8: _t->SetRange((*reinterpret_cast< QCPRange(*)>(_a[1]))); break;
        case 9: _t->PickPointCallBack((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 10: _t->SetAdminFuction((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 11: _t->on_actionSaveAs_triggered(); break;
        case 12: _t->on_actionImportCloud_triggered(); break;
        case 13: _t->on_actionScreenShot_triggered(); break;
        case 14: _t->on_actionRecord_triggered(); break;
        case 15: _t->on_actionStandBy_triggered(); break;
        case 16: _t->on_actionRunning_triggered(); break;
        case 17: _t->on_actionRecordCalibration_triggered(); break;
        case 18: _t->timerEvent((*reinterpret_cast< QTimerEvent*(*)>(_a[1]))); break;
        case 19: _t->on_actionPointcloudQuality_triggered(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (PointCloudsChart::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&PointCloudsChart::TransmitSerialCmd)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (PointCloudsChart::*)(std::vector<float> );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&PointCloudsChart::TransmitCalibrationData)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (PointCloudsChart::*)(std::vector<std::vector<float>> );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&PointCloudsChart::TransmitLidarInfo)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject PointCloudsChart::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_PointCloudsChart.data,
    qt_meta_data_PointCloudsChart,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *PointCloudsChart::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *PointCloudsChart::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_PointCloudsChart.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int PointCloudsChart::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 20)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 20;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 20)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 20;
    }
    return _id;
}

// SIGNAL 0
void PointCloudsChart::TransmitSerialCmd(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void PointCloudsChart::TransmitCalibrationData(std::vector<float> _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void PointCloudsChart::TransmitLidarInfo(std::vector<std::vector<float>> _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
