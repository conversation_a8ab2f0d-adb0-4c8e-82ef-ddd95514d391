/****************************************************************************
** Meta object code from reading C++ file 'import_pointcloud.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../import_pointcloud.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'import_pointcloud.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ImportPointCloud_t {
    QByteArrayData data[14];
    char stringdata0[286];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ImportPointCloud_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ImportPointCloud_t qt_meta_stringdata_ImportPointCloud = {
    {
QT_MOC_LITERAL(0, 0, 16), // "ImportPointCloud"
QT_MOC_LITERAL(1, 17, 22), // "transmitPointCloudData"
QT_MOC_LITERAL(2, 40, 0), // ""
QT_MOC_LITERAL(3, 41, 25), // "std::vector<ProtocolData>"
QT_MOC_LITERAL(4, 67, 4), // "data"
QT_MOC_LITERAL(5, 72, 23), // "transmitShowFileDataCmd"
QT_MOC_LITERAL(6, 96, 10), // "isFileData"
QT_MOC_LITERAL(7, 107, 7), // "TimeOut"
QT_MOC_LITERAL(8, 115, 37), // "on_pushButtonImportPointCloud..."
QT_MOC_LITERAL(9, 153, 26), // "on_pushButtonStart_clicked"
QT_MOC_LITERAL(10, 180, 25), // "on_pushButtonStop_clicked"
QT_MOC_LITERAL(11, 206, 30), // "on_radioButtonFileData_clicked"
QT_MOC_LITERAL(12, 237, 39), // "on_horizontalSliderSetSpeed_s..."
QT_MOC_LITERAL(13, 277, 8) // "position"

    },
    "ImportPointCloud\0transmitPointCloudData\0"
    "\0std::vector<ProtocolData>\0data\0"
    "transmitShowFileDataCmd\0isFileData\0"
    "TimeOut\0on_pushButtonImportPointCloud_clicked\0"
    "on_pushButtonStart_clicked\0"
    "on_pushButtonStop_clicked\0"
    "on_radioButtonFileData_clicked\0"
    "on_horizontalSliderSetSpeed_sliderMoved\0"
    "position"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ImportPointCloud[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   54,    2, 0x06 /* Public */,
       5,    1,   57,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       7,    0,   60,    2, 0x0a /* Public */,
       8,    0,   61,    2, 0x08 /* Private */,
       9,    0,   62,    2, 0x08 /* Private */,
      10,    0,   63,    2, 0x08 /* Private */,
      11,    0,   64,    2, 0x08 /* Private */,
      12,    1,   65,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::Bool,    6,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   13,

       0        // eod
};

void ImportPointCloud::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ImportPointCloud *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->transmitPointCloudData((*reinterpret_cast< std::vector<ProtocolData>(*)>(_a[1]))); break;
        case 1: _t->transmitShowFileDataCmd((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 2: _t->TimeOut(); break;
        case 3: _t->on_pushButtonImportPointCloud_clicked(); break;
        case 4: _t->on_pushButtonStart_clicked(); break;
        case 5: _t->on_pushButtonStop_clicked(); break;
        case 6: _t->on_radioButtonFileData_clicked(); break;
        case 7: _t->on_horizontalSliderSetSpeed_sliderMoved((*reinterpret_cast< int(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ImportPointCloud::*)(std::vector<ProtocolData> );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ImportPointCloud::transmitPointCloudData)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ImportPointCloud::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ImportPointCloud::transmitShowFileDataCmd)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ImportPointCloud::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_ImportPointCloud.data,
    qt_meta_data_ImportPointCloud,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ImportPointCloud::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ImportPointCloud::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ImportPointCloud.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ImportPointCloud::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void ImportPointCloud::transmitPointCloudData(std::vector<ProtocolData> _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ImportPointCloud::transmitShowFileDataCmd(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
