/********************************************************************************
** Form generated from reading UI file 'importpointcloud.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_IMPORTPOINTCLOUD_H
#define UI_IMPORTPOINTCLOUD_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QSlider>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ImportPointCloud
{
public:
    QGridLayout *gridLayout;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout;
    QPushButton *pushButtonImportPointCloud;
    QRadioButton *radioButtonFileData;
    QLineEdit *lineEditFilePath;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *pushButtonStart;
    QPushButton *pushButtonStop;
    QHBoxLayout *horizontalLayout_3;
    QLabel *label;
    QSlider *horizontalSliderSetSpeed;
    QLabel *label_2;
    QProgressBar *progressBar;

    void setupUi(QWidget *ImportPointCloud)
    {
        if (ImportPointCloud->objectName().isEmpty())
            ImportPointCloud->setObjectName(QString::fromUtf8("ImportPointCloud"));
        ImportPointCloud->resize(462, 199);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/download2.png"), QSize(), QIcon::Normal, QIcon::Off);
        ImportPointCloud->setWindowIcon(icon);
        ImportPointCloud->setStyleSheet(QString::fromUtf8("*{\n"
"	font: 75 12pt \"Agency FB\";\n"
"}\n"
"\n"
"QLabel\n"
"{ \n"
"	/*font: 25 10pt \"Microsoft YaHei\";  */\n"
"	border-radius: 10px;	\n"
"}\n"
"\n"
"QLineEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"QTextEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"/*\346\214\211\351\222\256\351\235\231\346\255\242\346\227\240\346\223\215\344\275\234\346\240\267\345\274\217*/\n"
"QPushButton \n"
"{\n"
"    /*background-color:blue;*/\n"
"    /*background-color: rgba(0, 102, 116, 240); \n"
"    color:white; */\n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt; */\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
" \n"
"/*\351\274\240\346\240\207\346\202\254\345\201\234\345\234\250\346\214\211\351\222\256*/\n"
"QPushButton:hover\n"
"{\n"
"    background-color: gray; \n"
"    color:rgb(6,168,255);\n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351"
                        "\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QPushButton:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"\n"
"/* === QGroupBox === */\n"
"QGroupBox {\n"
"    border: 2px solid gray;\n"
"    margin-top: 2ex;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    color: rgba(0, 102, 116, 240);\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    margin-left: 5px;\n"
"}\n"
"\n"
"\n"
"/* === QRadioButton === */\n"
"QRadioButton {\n"
"	/*background-color: rgba(0, 102, 116, 240); */\n"
"	/*color: white; */\n"
"    border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
"/* === QComboBox === */\n"
"QComboBox \n"
"{\n"
"	color:rgb(0,0,0);\n"
"	/*background:rgba(0, 102, 116, 240);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:hover\n"
"{\n"
"    color:gray;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
""
                        "QComboBox:pressed\n"
"{\n"
"    /*color:rgb(6,168,255);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:on\n"
"{\n"
"    /*color:rgb(6,168,255)*/\n"
"	/*color:rgb(255,255,255);*/;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item:selected\n"
"{	\n"
"	color:rgb(255,255,255);\n"
"	background-color:gray;\n"
"}\n"
"\n"
"\n"
"/********QGroupBox*******/\n"
"\n"
"QGroupBox \n"
"{\n"
"	color:rgb(6,168,255);\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"/********QToolBox*******/\n"
"QToolBox::tab {\n"
"    color: white;\n"
"	background-color: rgba(0, 102, 116, 240);\n"
"	font: 25 18pt \"Agency FB\";\n"
"    border: 2px solid gray;\n"
"    border-radius"
                        ":10px;\n"
"}\n"
"QToolBoxButton \n"
"{\n"
"    min-height: 40px; \n"
"	text-align: right;\n"
"}\n"
"\n"
"QToolBox::tab:hover\n"
"{\n"
"    color: gray;\n"
"	background-color: rgb(0, 170, 127);\n"
"	font: 25 18pt \"Agency FB\"; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QToolBox::tab:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"/*********/\n"
" \n"
"\n"
""));
        gridLayout = new QGridLayout(ImportPointCloud);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        pushButtonImportPointCloud = new QPushButton(ImportPointCloud);
        pushButtonImportPointCloud->setObjectName(QString::fromUtf8("pushButtonImportPointCloud"));
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(pushButtonImportPointCloud->sizePolicy().hasHeightForWidth());
        pushButtonImportPointCloud->setSizePolicy(sizePolicy);
        pushButtonImportPointCloud->setAutoFillBackground(false);
        pushButtonImportPointCloud->setFlat(false);

        horizontalLayout->addWidget(pushButtonImportPointCloud);

        radioButtonFileData = new QRadioButton(ImportPointCloud);
        radioButtonFileData->setObjectName(QString::fromUtf8("radioButtonFileData"));
        sizePolicy.setHeightForWidth(radioButtonFileData->sizePolicy().hasHeightForWidth());
        radioButtonFileData->setSizePolicy(sizePolicy);
        radioButtonFileData->setLayoutDirection(Qt::LeftToRight);

        horizontalLayout->addWidget(radioButtonFileData);

        lineEditFilePath = new QLineEdit(ImportPointCloud);
        lineEditFilePath->setObjectName(QString::fromUtf8("lineEditFilePath"));
        sizePolicy.setHeightForWidth(lineEditFilePath->sizePolicy().hasHeightForWidth());
        lineEditFilePath->setSizePolicy(sizePolicy);

        horizontalLayout->addWidget(lineEditFilePath);


        verticalLayout->addLayout(horizontalLayout);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        pushButtonStart = new QPushButton(ImportPointCloud);
        pushButtonStart->setObjectName(QString::fromUtf8("pushButtonStart"));
        sizePolicy.setHeightForWidth(pushButtonStart->sizePolicy().hasHeightForWidth());
        pushButtonStart->setSizePolicy(sizePolicy);

        horizontalLayout_2->addWidget(pushButtonStart);

        pushButtonStop = new QPushButton(ImportPointCloud);
        pushButtonStop->setObjectName(QString::fromUtf8("pushButtonStop"));
        sizePolicy.setHeightForWidth(pushButtonStop->sizePolicy().hasHeightForWidth());
        pushButtonStop->setSizePolicy(sizePolicy);

        horizontalLayout_2->addWidget(pushButtonStop);


        verticalLayout->addLayout(horizontalLayout_2);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        label = new QLabel(ImportPointCloud);
        label->setObjectName(QString::fromUtf8("label"));
        label->setAlignment(Qt::AlignCenter);

        horizontalLayout_3->addWidget(label);

        horizontalSliderSetSpeed = new QSlider(ImportPointCloud);
        horizontalSliderSetSpeed->setObjectName(QString::fromUtf8("horizontalSliderSetSpeed"));
        sizePolicy.setHeightForWidth(horizontalSliderSetSpeed->sizePolicy().hasHeightForWidth());
        horizontalSliderSetSpeed->setSizePolicy(sizePolicy);
        horizontalSliderSetSpeed->setMinimum(0);
        horizontalSliderSetSpeed->setMaximum(20);
        horizontalSliderSetSpeed->setPageStep(0);
        horizontalSliderSetSpeed->setValue(10);
        horizontalSliderSetSpeed->setTracking(false);
        horizontalSliderSetSpeed->setOrientation(Qt::Horizontal);
        horizontalSliderSetSpeed->setTickInterval(0);

        horizontalLayout_3->addWidget(horizontalSliderSetSpeed);

        label_2 = new QLabel(ImportPointCloud);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setAlignment(Qt::AlignCenter);

        horizontalLayout_3->addWidget(label_2);

        horizontalLayout_3->setStretch(0, 1);
        horizontalLayout_3->setStretch(1, 5);
        horizontalLayout_3->setStretch(2, 1);

        verticalLayout->addLayout(horizontalLayout_3);

        progressBar = new QProgressBar(ImportPointCloud);
        progressBar->setObjectName(QString::fromUtf8("progressBar"));
        sizePolicy.setHeightForWidth(progressBar->sizePolicy().hasHeightForWidth());
        progressBar->setSizePolicy(sizePolicy);
        progressBar->setValue(0);

        verticalLayout->addWidget(progressBar);

        verticalLayout->setStretch(0, 1);
        verticalLayout->setStretch(1, 2);
        verticalLayout->setStretch(2, 1);
        verticalLayout->setStretch(3, 1);

        gridLayout->addLayout(verticalLayout, 0, 0, 1, 1);


        retranslateUi(ImportPointCloud);

        QMetaObject::connectSlotsByName(ImportPointCloud);
    } // setupUi

    void retranslateUi(QWidget *ImportPointCloud)
    {
        ImportPointCloud->setWindowTitle(QCoreApplication::translate("ImportPointCloud", "importPointCloud", nullptr));
        pushButtonImportPointCloud->setText(QCoreApplication::translate("ImportPointCloud", "import", nullptr));
        radioButtonFileData->setText(QCoreApplication::translate("ImportPointCloud", "fileData", nullptr));
        pushButtonStart->setText(QCoreApplication::translate("ImportPointCloud", "Start", nullptr));
        pushButtonStop->setText(QCoreApplication::translate("ImportPointCloud", "Stop", nullptr));
        label->setText(QCoreApplication::translate("ImportPointCloud", "0hz", nullptr));
        label_2->setText(QCoreApplication::translate("ImportPointCloud", "20hz", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ImportPointCloud: public Ui_ImportPointCloud {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_IMPORTPOINTCLOUD_H
