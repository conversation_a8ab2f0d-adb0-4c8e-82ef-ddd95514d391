/********************************************************************************
** Form generated from reading UI file 'saveascapture.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SAVEASCAPTURE_H
#define UI_SAVEASCAPTURE_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_SaveAsCapture
{
public:
    QGridLayout *gridLayout;
    QVBoxLayout *verticalLayout;
    QTextEdit *textEditScreenShot;
    QHBoxLayout *horizontalLayout;
    QPushButton *pushButtonInsert;
    QPushButton *pushButtonClearAll;
    QPushButton *pushButtonSavePdf;
    QSpacerItem *horizontalSpacer;

    void setupUi(QWidget *SaveAsCapture)
    {
        if (SaveAsCapture->objectName().isEmpty())
            SaveAsCapture->setObjectName(QString::fromUtf8("SaveAsCapture"));
        SaveAsCapture->resize(506, 442);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/download.png"), QSize(), QIcon::Normal, QIcon::Off);
        SaveAsCapture->setWindowIcon(icon);
        SaveAsCapture->setStyleSheet(QString::fromUtf8("*{\n"
"	font: 75 12pt \"Agency FB\";\n"
"}\n"
"\n"
"QLabel\n"
"{ \n"
"	/*font: 25 10pt \"Microsoft YaHei\";  */\n"
"	border-radius: 10px;	\n"
"}\n"
"\n"
"QLineEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"QTextEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"/*\346\214\211\351\222\256\351\235\231\346\255\242\346\227\240\346\223\215\344\275\234\346\240\267\345\274\217*/\n"
"QPushButton \n"
"{\n"
"    /*background-color:blue;*/\n"
"    /*background-color: rgba(0, 102, 116, 240); \n"
"    color:white; */\n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt; */\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
" \n"
"/*\351\274\240\346\240\207\346\202\254\345\201\234\345\234\250\346\214\211\351\222\256*/\n"
"QPushButton:hover\n"
"{\n"
"    background-color: gray; \n"
"    color:rgb(6,168,255);\n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351"
                        "\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QPushButton:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"\n"
"/* === QGroupBox === */\n"
"QGroupBox {\n"
"    border: 2px solid gray;\n"
"    margin-top: 2ex;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    color: rgba(0, 102, 116, 240);\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    margin-left: 5px;\n"
"}\n"
"\n"
"\n"
"/* === QRadioButton === */\n"
"QRadioButton {\n"
"	/*background-color: rgba(0, 102, 116, 240); */\n"
"	/*color: white; */\n"
"    border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
"/* === QComboBox === */\n"
"QComboBox \n"
"{\n"
"	color:rgb(0,0,0);\n"
"	/*background:rgba(0, 102, 116, 240);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:hover\n"
"{\n"
"    color:gray;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
""
                        "QComboBox:pressed\n"
"{\n"
"    /*color:rgb(6,168,255);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:on\n"
"{\n"
"    /*color:rgb(6,168,255)*/\n"
"	/*color:rgb(255,255,255);*/;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item:selected\n"
"{	\n"
"	color:rgb(255,255,255);\n"
"	background-color:gray;\n"
"}\n"
"\n"
"\n"
"/********QGroupBox*******/\n"
"\n"
"QGroupBox \n"
"{\n"
"	color:rgb(6,168,255);\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"/********QToolBox*******/\n"
"QToolBox::tab {\n"
"    color: white;\n"
"	background-color: rgba(0, 102, 116, 240);\n"
"	font: 25 18pt \"Agency FB\";\n"
"    border: 2px solid gray;\n"
"    border-radius"
                        ":10px;\n"
"}\n"
"QToolBoxButton \n"
"{\n"
"    min-height: 40px; \n"
"	text-align: right;\n"
"}\n"
"\n"
"QToolBox::tab:hover\n"
"{\n"
"    color: gray;\n"
"	background-color: rgb(0, 170, 127);\n"
"	font: 25 18pt \"Agency FB\"; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QToolBox::tab:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"/*********/\n"
" \n"
"\n"
""));
        gridLayout = new QGridLayout(SaveAsCapture);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        textEditScreenShot = new QTextEdit(SaveAsCapture);
        textEditScreenShot->setObjectName(QString::fromUtf8("textEditScreenShot"));

        verticalLayout->addWidget(textEditScreenShot);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        pushButtonInsert = new QPushButton(SaveAsCapture);
        pushButtonInsert->setObjectName(QString::fromUtf8("pushButtonInsert"));
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(pushButtonInsert->sizePolicy().hasHeightForWidth());
        pushButtonInsert->setSizePolicy(sizePolicy);

        horizontalLayout->addWidget(pushButtonInsert);

        pushButtonClearAll = new QPushButton(SaveAsCapture);
        pushButtonClearAll->setObjectName(QString::fromUtf8("pushButtonClearAll"));
        sizePolicy.setHeightForWidth(pushButtonClearAll->sizePolicy().hasHeightForWidth());
        pushButtonClearAll->setSizePolicy(sizePolicy);

        horizontalLayout->addWidget(pushButtonClearAll);

        pushButtonSavePdf = new QPushButton(SaveAsCapture);
        pushButtonSavePdf->setObjectName(QString::fromUtf8("pushButtonSavePdf"));
        sizePolicy.setHeightForWidth(pushButtonSavePdf->sizePolicy().hasHeightForWidth());
        pushButtonSavePdf->setSizePolicy(sizePolicy);

        horizontalLayout->addWidget(pushButtonSavePdf);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);


        verticalLayout->addLayout(horizontalLayout);


        gridLayout->addLayout(verticalLayout, 0, 0, 1, 1);


        retranslateUi(SaveAsCapture);

        QMetaObject::connectSlotsByName(SaveAsCapture);
    } // setupUi

    void retranslateUi(QWidget *SaveAsCapture)
    {
        SaveAsCapture->setWindowTitle(QCoreApplication::translate("SaveAsCapture", "SaveScreenShot", nullptr));
        pushButtonInsert->setText(QCoreApplication::translate("SaveAsCapture", "Insert", nullptr));
        pushButtonClearAll->setText(QCoreApplication::translate("SaveAsCapture", "Clear All", nullptr));
        pushButtonSavePdf->setText(QCoreApplication::translate("SaveAsCapture", "Save Pdf", nullptr));
    } // retranslateUi

};

namespace Ui {
    class SaveAsCapture: public Ui_SaveAsCapture {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SAVEASCAPTURE_H
