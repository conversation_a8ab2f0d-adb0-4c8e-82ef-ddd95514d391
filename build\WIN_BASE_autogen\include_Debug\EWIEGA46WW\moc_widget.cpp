/****************************************************************************
** Meta object code from reading C++ file 'widget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../widget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'widget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_Widget_t {
    QByteArrayData data[45];
    char stringdata0[778];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_Widget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_Widget_t qt_meta_stringdata_Widget = {
    {
QT_MOC_LITERAL(0, 0, 6), // "Widget"
QT_MOC_LITERAL(1, 7, 15), // "QuitThreadLidar"
QT_MOC_LITERAL(2, 23, 0), // ""
QT_MOC_LITERAL(3, 24, 7), // "isClose"
QT_MOC_LITERAL(4, 32, 17), // "TransmitDataLidar"
QT_MOC_LITERAL(5, 50, 3), // "str"
QT_MOC_LITERAL(6, 54, 21), // "OpenSerialDeviceLidar"
QT_MOC_LITERAL(7, 76, 6), // "isOpen"
QT_MOC_LITERAL(8, 83, 7), // "comPort"
QT_MOC_LITERAL(9, 91, 4), // "buad"
QT_MOC_LITERAL(10, 96, 18), // "InitSerialPtrLidar"
QT_MOC_LITERAL(11, 115, 6), // "isInit"
QT_MOC_LITERAL(12, 122, 10), // "threadName"
QT_MOC_LITERAL(13, 133, 20), // "SetProtocolTypeLidar"
QT_MOC_LITERAL(14, 154, 12), // "protocolType"
QT_MOC_LITERAL(15, 167, 15), // "QuitThreadOther"
QT_MOC_LITERAL(16, 183, 17), // "TransmitDataOther"
QT_MOC_LITERAL(17, 201, 21), // "OpenSerialDeviceOther"
QT_MOC_LITERAL(18, 223, 18), // "InitSerialPtrOther"
QT_MOC_LITERAL(19, 242, 20), // "SetProtocolTypeOther"
QT_MOC_LITERAL(20, 263, 15), // "SetAdminFuction"
QT_MOC_LITERAL(21, 279, 8), // "isEnable"
QT_MOC_LITERAL(22, 288, 10), // "RecordData"
QT_MOC_LITERAL(23, 299, 4), // "isOk"
QT_MOC_LITERAL(24, 304, 12), // "ResizeButton"
QT_MOC_LITERAL(25, 317, 11), // "OpenedLidar"
QT_MOC_LITERAL(26, 329, 11), // "ClosedLidar"
QT_MOC_LITERAL(27, 341, 25), // "LidarSendToMainPointCloud"
QT_MOC_LITERAL(28, 367, 25), // "std::vector<ProtocolData>"
QT_MOC_LITERAL(29, 393, 4), // "data"
QT_MOC_LITERAL(30, 398, 11), // "OpenedOther"
QT_MOC_LITERAL(31, 410, 11), // "ClosedOther"
QT_MOC_LITERAL(32, 422, 25), // "OtherSendToMainPointCloud"
QT_MOC_LITERAL(33, 448, 21), // "on_pushButton_clicked"
QT_MOC_LITERAL(34, 470, 23), // "on_pushButton_2_clicked"
QT_MOC_LITERAL(35, 494, 7), // "timeOut"
QT_MOC_LITERAL(36, 502, 33), // "on_comboBox_baud1_editTextCha..."
QT_MOC_LITERAL(37, 536, 4), // "arg1"
QT_MOC_LITERAL(38, 541, 37), // "on_comboBox_baud1_currentInde..."
QT_MOC_LITERAL(39, 579, 37), // "on_comboBox_baud2_currentInde..."
QT_MOC_LITERAL(40, 617, 32), // "on_pushButton_HISTONGRAM_clicked"
QT_MOC_LITERAL(41, 650, 29), // "on_pushButton_GREYMAP_clicked"
QT_MOC_LITERAL(42, 680, 33), // "on_pushButton_CALIBRATION_cli..."
QT_MOC_LITERAL(43, 714, 32), // "on_pushButton_POINTCLOUD_clicked"
QT_MOC_LITERAL(44, 747, 30) // "on_pushButton_DATABASE_clicked"

    },
    "Widget\0QuitThreadLidar\0\0isClose\0"
    "TransmitDataLidar\0str\0OpenSerialDeviceLidar\0"
    "isOpen\0comPort\0buad\0InitSerialPtrLidar\0"
    "isInit\0threadName\0SetProtocolTypeLidar\0"
    "protocolType\0QuitThreadOther\0"
    "TransmitDataOther\0OpenSerialDeviceOther\0"
    "InitSerialPtrOther\0SetProtocolTypeOther\0"
    "SetAdminFuction\0isEnable\0RecordData\0"
    "isOk\0ResizeButton\0OpenedLidar\0ClosedLidar\0"
    "LidarSendToMainPointCloud\0"
    "std::vector<ProtocolData>\0data\0"
    "OpenedOther\0ClosedOther\0"
    "OtherSendToMainPointCloud\0"
    "on_pushButton_clicked\0on_pushButton_2_clicked\0"
    "timeOut\0on_comboBox_baud1_editTextChanged\0"
    "arg1\0on_comboBox_baud1_currentIndexChanged\0"
    "on_comboBox_baud2_currentIndexChanged\0"
    "on_pushButton_HISTONGRAM_clicked\0"
    "on_pushButton_GREYMAP_clicked\0"
    "on_pushButton_CALIBRATION_clicked\0"
    "on_pushButton_POINTCLOUD_clicked\0"
    "on_pushButton_DATABASE_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_Widget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      30,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      12,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,  164,    2, 0x06 /* Public */,
       4,    1,  167,    2, 0x06 /* Public */,
       6,    3,  170,    2, 0x06 /* Public */,
      10,    2,  177,    2, 0x06 /* Public */,
      13,    1,  182,    2, 0x06 /* Public */,
      15,    1,  185,    2, 0x06 /* Public */,
      16,    1,  188,    2, 0x06 /* Public */,
      17,    3,  191,    2, 0x06 /* Public */,
      18,    2,  198,    2, 0x06 /* Public */,
      19,    1,  203,    2, 0x06 /* Public */,
      20,    1,  206,    2, 0x06 /* Public */,
      22,    1,  209,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      24,    0,  212,    2, 0x08 /* Private */,
      25,    0,  213,    2, 0x08 /* Private */,
      26,    0,  214,    2, 0x08 /* Private */,
      27,    1,  215,    2, 0x08 /* Private */,
      30,    0,  218,    2, 0x08 /* Private */,
      31,    0,  219,    2, 0x08 /* Private */,
      32,    1,  220,    2, 0x08 /* Private */,
      33,    0,  223,    2, 0x08 /* Private */,
      34,    0,  224,    2, 0x08 /* Private */,
      35,    0,  225,    2, 0x08 /* Private */,
      36,    1,  226,    2, 0x08 /* Private */,
      38,    1,  229,    2, 0x08 /* Private */,
      39,    1,  232,    2, 0x08 /* Private */,
      40,    0,  235,    2, 0x08 /* Private */,
      41,    0,  236,    2, 0x08 /* Private */,
      42,    0,  237,    2, 0x08 /* Private */,
      43,    0,  238,    2, 0x08 /* Private */,
      44,    0,  239,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Bool,    3,
    QMetaType::Void, QMetaType::QByteArray,    5,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString, QMetaType::Int,    7,    8,    9,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,   11,   12,
    QMetaType::Void, QMetaType::Int,   14,
    QMetaType::Void, QMetaType::Bool,    3,
    QMetaType::Void, QMetaType::QByteArray,    5,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString, QMetaType::Int,    7,    8,    9,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,   11,   12,
    QMetaType::Void, QMetaType::Int,   14,
    QMetaType::Void, QMetaType::Bool,   21,
    QMetaType::Void, QMetaType::Bool,   23,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 28,   29,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 28,   29,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   37,
    QMetaType::Void, QMetaType::QString,   37,
    QMetaType::Void, QMetaType::QString,   37,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void Widget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<Widget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->QuitThreadLidar((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 1: _t->TransmitDataLidar((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 2: _t->OpenSerialDeviceLidar((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 3: _t->InitSerialPtrLidar((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2]))); break;
        case 4: _t->SetProtocolTypeLidar((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 5: _t->QuitThreadOther((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 6: _t->TransmitDataOther((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 7: _t->OpenSerialDeviceOther((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 8: _t->InitSerialPtrOther((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2]))); break;
        case 9: _t->SetProtocolTypeOther((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 10: _t->SetAdminFuction((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 11: _t->RecordData((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 12: _t->ResizeButton(); break;
        case 13: _t->OpenedLidar(); break;
        case 14: _t->ClosedLidar(); break;
        case 15: _t->LidarSendToMainPointCloud((*reinterpret_cast< std::vector<ProtocolData>(*)>(_a[1]))); break;
        case 16: _t->OpenedOther(); break;
        case 17: _t->ClosedOther(); break;
        case 18: _t->OtherSendToMainPointCloud((*reinterpret_cast< std::vector<ProtocolData>(*)>(_a[1]))); break;
        case 19: _t->on_pushButton_clicked(); break;
        case 20: _t->on_pushButton_2_clicked(); break;
        case 21: _t->timeOut(); break;
        case 22: _t->on_comboBox_baud1_editTextChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 23: _t->on_comboBox_baud1_currentIndexChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 24: _t->on_comboBox_baud2_currentIndexChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 25: _t->on_pushButton_HISTONGRAM_clicked(); break;
        case 26: _t->on_pushButton_GREYMAP_clicked(); break;
        case 27: _t->on_pushButton_CALIBRATION_clicked(); break;
        case 28: _t->on_pushButton_POINTCLOUD_clicked(); break;
        case 29: _t->on_pushButton_DATABASE_clicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (Widget::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::QuitThreadLidar)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (Widget::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::TransmitDataLidar)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (Widget::*)(bool , QString , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::OpenSerialDeviceLidar)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (Widget::*)(bool , QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::InitSerialPtrLidar)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (Widget::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::SetProtocolTypeLidar)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (Widget::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::QuitThreadOther)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (Widget::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::TransmitDataOther)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (Widget::*)(bool , QString , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::OpenSerialDeviceOther)) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (Widget::*)(bool , QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::InitSerialPtrOther)) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (Widget::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::SetProtocolTypeOther)) {
                *result = 9;
                return;
            }
        }
        {
            using _t = void (Widget::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::SetAdminFuction)) {
                *result = 10;
                return;
            }
        }
        {
            using _t = void (Widget::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Widget::RecordData)) {
                *result = 11;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject Widget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_Widget.data,
    qt_meta_data_Widget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *Widget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Widget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_Widget.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "QAbstractNativeEventFilter"))
        return static_cast< QAbstractNativeEventFilter*>(this);
    return QWidget::qt_metacast(_clname);
}

int Widget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 30)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 30;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 30)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 30;
    }
    return _id;
}

// SIGNAL 0
void Widget::QuitThreadLidar(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void Widget::TransmitDataLidar(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void Widget::OpenSerialDeviceLidar(bool _t1, QString _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void Widget::InitSerialPtrLidar(bool _t1, QString _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void Widget::SetProtocolTypeLidar(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void Widget::QuitThreadOther(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void Widget::TransmitDataOther(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void Widget::OpenSerialDeviceOther(bool _t1, QString _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void Widget::InitSerialPtrOther(bool _t1, QString _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}

// SIGNAL 9
void Widget::SetProtocolTypeOther(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 9, _a);
}

// SIGNAL 10
void Widget::SetAdminFuction(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 10, _a);
}

// SIGNAL 11
void Widget::RecordData(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 11, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
