#ifndef POINTCLOUD_QUALITY_H
#define POINTCLOUD_QUALITY_H

#include <QMainWindow>
#include "serial_base.h"
enum
{
    IMAGE_MATERIAL_WHITE = 0,
    IMAGE_MATERIAL_WOODGRAIN,
};
typedef struct STPOINTCLOUDQUALITY{
    float standardAngle;
    float standardDistance;
    float globalAngleL;
    float globalAngleR;
    uint16_t sampleNum;
    uint16_t threshold;
    float driftAngleStandard;
    float outlierRateStandard;
    float outlierControlDistanceStandar;
    float materialType;
    float breakAngleStandard;
    float breakAngle1RangeL;
    float breakAngle1RangeR;
    float breakAngle2RangeL;
    float breakAngle2RangeR;
    STPOINTCLOUDQUALITY()
    {
         standardAngle = 90.0;
         standardDistance = 400;;
         globalAngleL = 360-30.6;
         globalAngleR = 30.6;
         sampleNum = 10;
         threshold = 8;
         driftAngleStandard = 0.9;
         outlierRateStandard = 4;
         outlierControlDistanceStandar =6;
         materialType = IMAGE_MATERIAL_WHITE;
         breakAngleStandard = 1.4;
         breakAngle1RangeL = 20;
         breakAngle1RangeR = 50;
         breakAngle2RangeL = 130;
         breakAngle2RangeR = 160;
    }
}StPointCloudQuality;
namespace Ui {
class PointCloudQuality;
}

class PointCloudQuality : public QMainWindow
{
    Q_OBJECT

public:
    explicit PointCloudQuality(QWidget *parent = nullptr);
    ~PointCloudQuality();

private slots:
    void on_btn_Cal_clicked();
    //void ReceivePointCloudData(std::vector<ProtocolData> data);

private:
    Ui::PointCloudQuality *ui;
    StPointCloudQuality _stPointCloudQuality;
};

#endif // POINTCLOUD_QUALITY_H
