#ifndef POINTCLOUDS_CHART_H
#define POINTCLOUDS_CHART_H

#include <QMainWindow>
#include <customPlot/qcustomplot.h>
#include <string.h>
#include <iostream>
#include <math.h>
#include "serial_base.h"
#include "saveas_capture.h"
#include "record_pointcloud.h"
#include "import_pointcloud.h"
#include "dynamic_calibration.h"
#include "pointcloud_quality.h"




namespace Ui {
class PointCloudsChart;
}

class PointCloudsChart : public QMainWindow
{
    Q_OBJECT

public:
    explicit PointCloudsChart(QWidget *parent = nullptr);
    ~PointCloudsChart();
    void GuiSetup(void);

    const uint16_t kMaxRadius = 16000;
    const int16_t  kActionHeight = 80;
    const uint8_t  kEllipseCnt = 5;
    const uint16_t kStatisticalMax = 50;
    enum {
        kRange0 = 32000,
        kRange1 = 16000,
        kRange2 = 8000,
        kRange3 = 4000,
        kRange4 = 2000,
        kRange5 = 1000,
        kRange6 = 500,
        kRange7 = 250,
        kRange8 = 125,
        kRange9 = 62,
    };

//    void TransmitAngle(float dat);
//    void ReceiveAngle(float dat);
//    void SendAngleCmd(float dat);
    std::vector<float>  CalcStatistics(std::vector<uint16_t> data);
    int  samsung_calc_bound(std::vector<float> in_angle, std::vector<float> in_dis, std::vector<float> in_intensity, std::vector<float> *param);
    int samsung_calc_bound_tri(std::vector<float> angle, std::vector<float> dis, std::vector<float> intensity, std::vector<float> *param);

signals:
    void TransmitSerialCmd(QByteArray data);
    void TransmitCalibrationData(std::vector<float> data);
    void TransmitLidarInfo(std::vector<std::vector<float>> data);


public slots:
    void ReceivePointCloudData(std::vector<ProtocolData> data);
    void ReceiveFileData(std::vector<ProtocolData> data);
    void RecShowFileDataCmd(bool isFileData);
    void RecStartRecord(QString fileName);
    void RecStopRecord();
    void SetRange(QCPRange range);
    void PickPointCallBack(QMouseEvent *event);
    void SetAdminFuction(bool isEnable);

protected:
    virtual  void resizeEvent(QResizeEvent *event);

private slots:
    void on_actionSaveAs_triggered();

    void on_actionImportCloud_triggered();

    void on_actionScreenShot_triggered();

    void on_actionRecord_triggered();

    void on_actionStandBy_triggered();

    void on_actionRunning_triggered();

    void on_actionRecordCalibration_triggered();

    void timerEvent(QTimerEvent *event);

    void on_actionPointcloudQuality_triggered();

private:
    Ui::PointCloudsChart *ui;
    QCustomPlot *customPlot = nullptr;
    SaveAsCapture *saveAsCapturePtr = nullptr;
    RecordPointCloud *recordPointCloud = nullptr;
    ImportPointCloud *importPointCloudPtr = nullptr;
    DynamicCalibration *dynamicCalibration = nullptr;
    PointCloudQuality *pointCloudQualityPtr = nullptr;


    bool  isStartRecord,isShowFileData,isRecordPoint;
    QString fName,fPointName;
    uint32_t recordFrameCnt;
    uint16_t curStatisticsCnt;
    QString angleName;

    double widgetRate;
    uint16_t currentRaduis;

    double pickPointAngle;

    std::vector<ProtocolData>  lastData;

    std::vector<QCPItemEllipse*> qcpItemEll;
    std::map<uint8_t,float> ellipseFactor;
    std::map<QString,QCPItemText*> qcpLidarInfoText;

    std::vector<QCPItemText*> qcpText;
    std::vector<QCPItemLine*> qcpLine;

    QCPItemLine *pickLine  = nullptr;
    QCPItemText *version = nullptr;
    QCPItemText *speed = nullptr;
    QCPItemText *pick = nullptr;
    QCPItemText *mean = nullptr;
    QCPItemText *others = nullptr;
    std::vector<uint16_t> accumalateDis,accumalatePeak;

    uint m_timerId;
    bool isExeCompensation;
    bool isExeSample,isExeCompare,isExeBaseAngle;
    float samplePt,samplePt1,baseAngle;
    float degree_factor;

    bool is_integration_pointcloud;



};

#endif // POINTCLOUDS_CHART_H
