/********************************************************************************
** Form generated from reading UI file 'pointcloudschart.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_POINTCLOUDSCHART_H
#define UI_POINTCLOUDSCHART_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QWidget>
#include "qcustomplot.h"

QT_BEGIN_NAMESPACE

class Ui_PointCloudsChart
{
public:
    QAction *actionStandBy;
    QAction *actionRunning;
    QAction *actionScreenShot;
    QAction *actionSaveAs;
    QAction *actionRecord;
    QAction *actionImportCloud;
    QAction *actionRecordCalibration;
    QAction *actionRecord_4;
    QAction *actionPointcloudQuality;
    QWidget *centralwidget;
    QGridLayout *gridLayout;
    QCustomPlot *customPlot;
    QToolBar *toolBar;

    void setupUi(QMainWindow *PointCloudsChart)
    {
        if (PointCloudsChart->objectName().isEmpty())
            PointCloudsChart->setObjectName(QString::fromUtf8("PointCloudsChart"));
        PointCloudsChart->resize(969, 750);
        PointCloudsChart->setCursor(QCursor(Qt::ArrowCursor));
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/sacn.png"), QSize(), QIcon::Normal, QIcon::Off);
        PointCloudsChart->setWindowIcon(icon);
        PointCloudsChart->setLayoutDirection(Qt::RightToLeft);
        PointCloudsChart->setAutoFillBackground(true);
        PointCloudsChart->setStyleSheet(QString::fromUtf8("*{\n"
"	font: 75 12pt \"Agency FB\";\n"
"}\n"
"\n"
"QLabel\n"
"{ \n"
"	/*font: 25 10pt \"Microsoft YaHei\";  */\n"
"	border-radius: 10px;	\n"
"}\n"
"\n"
"QLineEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"QTextEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"/*\346\214\211\351\222\256\351\235\231\346\255\242\346\227\240\346\223\215\344\275\234\346\240\267\345\274\217*/\n"
"QPushButton \n"
"{\n"
"    /*background-color:blue;*/\n"
"    /*background-color: rgba(0, 102, 116, 240); \n"
"    color:white; */\n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt; */\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
" \n"
"/*\351\274\240\346\240\207\346\202\254\345\201\234\345\234\250\346\214\211\351\222\256*/\n"
"QPushButton:hover\n"
"{\n"
"    background-color: gray; \n"
"    color:rgb(6,168,255);\n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351"
                        "\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QPushButton:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"\n"
"/* === QGroupBox === */\n"
"QGroupBox {\n"
"    border: 2px solid gray;\n"
"    margin-top: 2ex;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    color: rgba(0, 102, 116, 240);\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    margin-left: 5px;\n"
"}\n"
"\n"
"\n"
"/* === QRadioButton === */\n"
"QRadioButton {\n"
"	/*background-color: rgba(0, 102, 116, 240); */\n"
"	/*color: white; */\n"
"    border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
"/* === QComboBox === */\n"
"QComboBox \n"
"{\n"
"	color:rgb(0,0,0);\n"
"	/*background:rgba(0, 102, 116, 240);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:hover\n"
"{\n"
"    color:gray;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
""
                        "QComboBox:pressed\n"
"{\n"
"    /*color:rgb(6,168,255);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:on\n"
"{\n"
"    /*color:rgb(6,168,255)*/\n"
"	/*color:rgb(255,255,255);*/;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item:selected\n"
"{	\n"
"	color:rgb(255,255,255);\n"
"	background-color:gray;\n"
"}\n"
"\n"
"\n"
"/********QGroupBox*******/\n"
"\n"
"QGroupBox \n"
"{\n"
"	color:rgb(6,168,255);\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"/********QToolBox*******/\n"
"QToolBox::tab {\n"
"    color: white;\n"
"	background-color: rgba(0, 102, 116, 240);\n"
"	font: 25 18pt \"Agency FB\";\n"
"    border: 2px solid gray;\n"
"    border-radius"
                        ":10px;\n"
"}\n"
"QToolBoxButton \n"
"{\n"
"    min-height: 40px; \n"
"	text-align: right;\n"
"}\n"
"\n"
"QToolBox::tab:hover\n"
"{\n"
"    color: gray;\n"
"	background-color: rgb(0, 170, 127);\n"
"	font: 25 18pt \"Agency FB\"; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QToolBox::tab:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"/*********/\n"
" \n"
"\n"
""));
        actionStandBy = new QAction(PointCloudsChart);
        actionStandBy->setObjectName(QString::fromUtf8("actionStandBy"));
        actionStandBy->setCheckable(false);
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/new/prefix1/icon/stop2.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionStandBy->setIcon(icon1);
        actionRunning = new QAction(PointCloudsChart);
        actionRunning->setObjectName(QString::fromUtf8("actionRunning"));
        actionRunning->setCheckable(false);
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/new/prefix1/icon/running2.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionRunning->setIcon(icon2);
        actionScreenShot = new QAction(PointCloudsChart);
        actionScreenShot->setObjectName(QString::fromUtf8("actionScreenShot"));
        actionScreenShot->setCheckable(false);
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/shot.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionScreenShot->setIcon(icon3);
        actionSaveAs = new QAction(PointCloudsChart);
        actionSaveAs->setObjectName(QString::fromUtf8("actionSaveAs"));
        actionSaveAs->setCheckable(false);
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/download2.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionSaveAs->setIcon(icon4);
        actionRecord = new QAction(PointCloudsChart);
        actionRecord->setObjectName(QString::fromUtf8("actionRecord"));
        actionRecord->setCheckable(false);
        QIcon icon5;
        icon5.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/record.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionRecord->setIcon(icon5);
        actionImportCloud = new QAction(PointCloudsChart);
        actionImportCloud->setObjectName(QString::fromUtf8("actionImportCloud"));
        actionImportCloud->setCheckable(false);
        QIcon icon6;
        icon6.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/import.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionImportCloud->setIcon(icon6);
        actionRecordCalibration = new QAction(PointCloudsChart);
        actionRecordCalibration->setObjectName(QString::fromUtf8("actionRecordCalibration"));
        actionRecordCalibration->setCheckable(false);
        QIcon icon7;
        icon7.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/fit2.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionRecordCalibration->setIcon(icon7);
        actionRecord_4 = new QAction(PointCloudsChart);
        actionRecord_4->setObjectName(QString::fromUtf8("actionRecord_4"));
        actionRecord_4->setCheckable(false);
        QIcon icon8;
        icon8.addFile(QString::fromUtf8(":/icon/logo.ico"), QSize(), QIcon::Normal, QIcon::Off);
        actionRecord_4->setIcon(icon8);
        actionPointcloudQuality = new QAction(PointCloudsChart);
        actionPointcloudQuality->setObjectName(QString::fromUtf8("actionPointcloudQuality"));
        QIcon icon9;
        icon9.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/calibration3.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionPointcloudQuality->setIcon(icon9);
        centralwidget = new QWidget(PointCloudsChart);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        gridLayout = new QGridLayout(centralwidget);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setContentsMargins(0, 0, 0, 0);
        customPlot = new QCustomPlot(centralwidget);
        customPlot->setObjectName(QString::fromUtf8("customPlot"));
        customPlot->setAutoFillBackground(true);

        gridLayout->addWidget(customPlot, 0, 0, 1, 1);

        PointCloudsChart->setCentralWidget(centralwidget);
        toolBar = new QToolBar(PointCloudsChart);
        toolBar->setObjectName(QString::fromUtf8("toolBar"));
        QSizePolicy sizePolicy(QSizePolicy::Fixed, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(toolBar->sizePolicy().hasHeightForWidth());
        toolBar->setSizePolicy(sizePolicy);
        QFont font;
        font.setFamily(QString::fromUtf8("Agency FB"));
        font.setPointSize(12);
        font.setBold(false);
        font.setItalic(false);
        font.setUnderline(false);
        font.setWeight(9);
        font.setKerning(true);
        font.setStyleStrategy(QFont::PreferDefault);
        toolBar->setFont(font);
        toolBar->setTabletTracking(false);
        toolBar->setAcceptDrops(true);
        toolBar->setAutoFillBackground(false);
        toolBar->setAllowedAreas(Qt::AllToolBarAreas);
        toolBar->setIconSize(QSize(50, 50));
        toolBar->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);
        toolBar->setFloatable(true);
        PointCloudsChart->addToolBar(Qt::LeftToolBarArea, toolBar);

        toolBar->addAction(actionStandBy);
        toolBar->addAction(actionRunning);
        toolBar->addSeparator();
        toolBar->addAction(actionScreenShot);
        toolBar->addAction(actionSaveAs);
        toolBar->addSeparator();
        toolBar->addAction(actionRecord);
        toolBar->addAction(actionImportCloud);
        toolBar->addAction(actionRecordCalibration);
        toolBar->addAction(actionPointcloudQuality);

        retranslateUi(PointCloudsChart);

        QMetaObject::connectSlotsByName(PointCloudsChart);
    } // setupUi

    void retranslateUi(QMainWindow *PointCloudsChart)
    {
        PointCloudsChart->setWindowTitle(QCoreApplication::translate("PointCloudsChart", "PointCloud", nullptr));
        actionStandBy->setText(QCoreApplication::translate("PointCloudsChart", "StandBy", nullptr));
#if QT_CONFIG(tooltip)
        actionStandBy->setToolTip(QCoreApplication::translate("PointCloudsChart", "\345\201\234\346\255\242\351\233\267\350\276\276", nullptr));
#endif // QT_CONFIG(tooltip)
        actionRunning->setText(QCoreApplication::translate("PointCloudsChart", "TurnOn", nullptr));
#if QT_CONFIG(tooltip)
        actionRunning->setToolTip(QCoreApplication::translate("PointCloudsChart", "\350\277\220\350\241\214\351\233\267\350\276\276", nullptr));
#endif // QT_CONFIG(tooltip)
        actionScreenShot->setText(QCoreApplication::translate("PointCloudsChart", "Scrshot", nullptr));
#if QT_CONFIG(tooltip)
        actionScreenShot->setToolTip(QCoreApplication::translate("PointCloudsChart", "\346\210\252\345\233\276", nullptr));
#endif // QT_CONFIG(tooltip)
        actionSaveAs->setText(QCoreApplication::translate("PointCloudsChart", "SaveAs", nullptr));
#if QT_CONFIG(tooltip)
        actionSaveAs->setToolTip(QCoreApplication::translate("PointCloudsChart", "\346\210\252\345\233\276\344\277\235\345\255\230", nullptr));
#endif // QT_CONFIG(tooltip)
        actionRecord->setText(QCoreApplication::translate("PointCloudsChart", "Record", nullptr));
#if QT_CONFIG(tooltip)
        actionRecord->setToolTip(QCoreApplication::translate("PointCloudsChart", "\350\256\260\345\275\225\346\225\260\346\215\256", nullptr));
#endif // QT_CONFIG(tooltip)
        actionImportCloud->setText(QCoreApplication::translate("PointCloudsChart", "ImtPtCld", nullptr));
#if QT_CONFIG(tooltip)
        actionImportCloud->setToolTip(QCoreApplication::translate("PointCloudsChart", "\345\257\274\345\205\245\346\225\260\346\215\256", nullptr));
#endif // QT_CONFIG(tooltip)
        actionRecordCalibration->setText(QCoreApplication::translate("PointCloudsChart", "Calibra", nullptr));
#if QT_CONFIG(tooltip)
        actionRecordCalibration->setToolTip(QCoreApplication::translate("PointCloudsChart", "\350\256\241\347\256\227\346\240\241\346\255\243\345\217\202\346\225\260", nullptr));
#endif // QT_CONFIG(tooltip)
        actionRecord_4->setText(QCoreApplication::translate("PointCloudsChart", "Record", nullptr));
#if QT_CONFIG(tooltip)
        actionRecord_4->setToolTip(QCoreApplication::translate("PointCloudsChart", "Record", nullptr));
#endif // QT_CONFIG(tooltip)
        actionPointcloudQuality->setText(QCoreApplication::translate("PointCloudsChart", "ImageQuality", nullptr));
#if QT_CONFIG(tooltip)
        actionPointcloudQuality->setToolTip(QCoreApplication::translate("PointCloudsChart", "\350\256\241\347\256\227\347\202\271\344\272\221\350\264\250\351\207\217", nullptr));
#endif // QT_CONFIG(tooltip)
        toolBar->setWindowTitle(QCoreApplication::translate("PointCloudsChart", "toolBar", nullptr));
    } // retranslateUi

};

namespace Ui {
    class PointCloudsChart: public Ui_PointCloudsChart {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_POINTCLOUDSCHART_H
