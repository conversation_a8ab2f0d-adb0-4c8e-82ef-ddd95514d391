/****************************************************************************
** Meta object code from reading C++ file 'calibration_chart.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../calibration_chart.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'calibration_chart.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CalibrationChart_t {
    QByteArrayData data[35];
    char stringdata0[631];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CalibrationChart_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CalibrationChart_t qt_meta_stringdata_CalibrationChart = {
    {
QT_MOC_LITERAL(0, 0, 16), // "CalibrationChart"
QT_MOC_LITERAL(1, 17, 23), // "TransmitCalibrationData"
QT_MOC_LITERAL(2, 41, 0), // ""
QT_MOC_LITERAL(3, 42, 18), // "std::vector<float>"
QT_MOC_LITERAL(4, 61, 1), // "p"
QT_MOC_LITERAL(5, 63, 11), // "TransmitCmd"
QT_MOC_LITERAL(6, 75, 4), // "data"
QT_MOC_LITERAL(7, 80, 15), // "TransmitTestCmd"
QT_MOC_LITERAL(8, 96, 22), // "receiveCalibrationData"
QT_MOC_LITERAL(9, 119, 3), // "rec"
QT_MOC_LITERAL(10, 123, 17), // "FeedbackInfoLidar"
QT_MOC_LITERAL(11, 141, 3), // "fdb"
QT_MOC_LITERAL(12, 145, 13), // "onGetMousePos"
QT_MOC_LITERAL(13, 159, 3), // "pos"
QT_MOC_LITERAL(14, 163, 12), // "onMenuAction"
QT_MOC_LITERAL(15, 176, 8), // "QAction*"
QT_MOC_LITERAL(16, 185, 3), // "act"
QT_MOC_LITERAL(17, 189, 14), // "receiveHistDis"
QT_MOC_LITERAL(18, 204, 3), // "dis"
QT_MOC_LITERAL(19, 208, 16), // "ReceiveLidarInfo"
QT_MOC_LITERAL(20, 225, 32), // "std::vector<std::vector<float> >"
QT_MOC_LITERAL(21, 258, 30), // "on_pushButton_SAVE_LOG_clicked"
QT_MOC_LITERAL(22, 289, 31), // "on_pushButton_CLEAR_LOG_clicked"
QT_MOC_LITERAL(23, 321, 18), // "on_stopCMD_clicked"
QT_MOC_LITERAL(24, 340, 19), // "on_resetCMD_clicked"
QT_MOC_LITERAL(25, 360, 29), // "on_settingSpeed_returnPressed"
QT_MOC_LITERAL(26, 390, 17), // "on_RunDis_clicked"
QT_MOC_LITERAL(27, 408, 25), // "on_addSamplePoint_clicked"
QT_MOC_LITERAL(28, 434, 17), // "on_exeRun_clicked"
QT_MOC_LITERAL(29, 452, 31), // "on_samplePointDis_returnPressed"
QT_MOC_LITERAL(30, 484, 23), // "on_offset_returnPressed"
QT_MOC_LITERAL(31, 508, 23), // "on_exeNum_returnPressed"
QT_MOC_LITERAL(32, 532, 29), // "on_saveFileName_returnPressed"
QT_MOC_LITERAL(33, 562, 32), // "on_settingDistance_returnPressed"
QT_MOC_LITERAL(34, 595, 35) // "on_lineEdit_FILE_NAME_returnP..."

    },
    "CalibrationChart\0TransmitCalibrationData\0"
    "\0std::vector<float>\0p\0TransmitCmd\0"
    "data\0TransmitTestCmd\0receiveCalibrationData\0"
    "rec\0FeedbackInfoLidar\0fdb\0onGetMousePos\0"
    "pos\0onMenuAction\0QAction*\0act\0"
    "receiveHistDis\0dis\0ReceiveLidarInfo\0"
    "std::vector<std::vector<float> >\0"
    "on_pushButton_SAVE_LOG_clicked\0"
    "on_pushButton_CLEAR_LOG_clicked\0"
    "on_stopCMD_clicked\0on_resetCMD_clicked\0"
    "on_settingSpeed_returnPressed\0"
    "on_RunDis_clicked\0on_addSamplePoint_clicked\0"
    "on_exeRun_clicked\0on_samplePointDis_returnPressed\0"
    "on_offset_returnPressed\0on_exeNum_returnPressed\0"
    "on_saveFileName_returnPressed\0"
    "on_settingDistance_returnPressed\0"
    "on_lineEdit_FILE_NAME_returnPressed"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CalibrationChart[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      23,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,  129,    2, 0x06 /* Public */,
       5,    1,  132,    2, 0x06 /* Public */,
       7,    1,  135,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    1,  138,    2, 0x0a /* Public */,
      10,    1,  141,    2, 0x0a /* Public */,
      12,    1,  144,    2, 0x0a /* Public */,
      14,    1,  147,    2, 0x0a /* Public */,
      17,    1,  150,    2, 0x0a /* Public */,
      19,    1,  153,    2, 0x0a /* Public */,
      21,    0,  156,    2, 0x08 /* Private */,
      22,    0,  157,    2, 0x08 /* Private */,
      23,    0,  158,    2, 0x08 /* Private */,
      24,    0,  159,    2, 0x08 /* Private */,
      25,    0,  160,    2, 0x08 /* Private */,
      26,    0,  161,    2, 0x08 /* Private */,
      27,    0,  162,    2, 0x08 /* Private */,
      28,    0,  163,    2, 0x08 /* Private */,
      29,    0,  164,    2, 0x08 /* Private */,
      30,    0,  165,    2, 0x08 /* Private */,
      31,    0,  166,    2, 0x08 /* Private */,
      32,    0,  167,    2, 0x08 /* Private */,
      33,    0,  168,    2, 0x08 /* Private */,
      34,    0,  169,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::QByteArray,    6,
    QMetaType::Void, QMetaType::QByteArray,    6,

 // slots: parameters
    QMetaType::Void, QMetaType::QByteArray,    9,
    QMetaType::Void, QMetaType::QByteArray,   11,
    QMetaType::Void, QMetaType::QPoint,   13,
    QMetaType::Void, 0x80000000 | 15,   16,
    QMetaType::Void, QMetaType::Float,   18,
    QMetaType::Void, 0x80000000 | 20,    6,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void CalibrationChart::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CalibrationChart *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->TransmitCalibrationData((*reinterpret_cast< std::vector<float>(*)>(_a[1]))); break;
        case 1: _t->TransmitCmd((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 2: _t->TransmitTestCmd((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 3: _t->receiveCalibrationData((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 4: _t->FeedbackInfoLidar((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 5: _t->onGetMousePos((*reinterpret_cast< QPoint(*)>(_a[1]))); break;
        case 6: _t->onMenuAction((*reinterpret_cast< QAction*(*)>(_a[1]))); break;
        case 7: _t->receiveHistDis((*reinterpret_cast< float(*)>(_a[1]))); break;
        case 8: _t->ReceiveLidarInfo((*reinterpret_cast< std::vector<std::vector<float> >(*)>(_a[1]))); break;
        case 9: _t->on_pushButton_SAVE_LOG_clicked(); break;
        case 10: _t->on_pushButton_CLEAR_LOG_clicked(); break;
        case 11: _t->on_stopCMD_clicked(); break;
        case 12: _t->on_resetCMD_clicked(); break;
        case 13: _t->on_settingSpeed_returnPressed(); break;
        case 14: _t->on_RunDis_clicked(); break;
        case 15: _t->on_addSamplePoint_clicked(); break;
        case 16: _t->on_exeRun_clicked(); break;
        case 17: _t->on_samplePointDis_returnPressed(); break;
        case 18: _t->on_offset_returnPressed(); break;
        case 19: _t->on_exeNum_returnPressed(); break;
        case 20: _t->on_saveFileName_returnPressed(); break;
        case 21: _t->on_settingDistance_returnPressed(); break;
        case 22: _t->on_lineEdit_FILE_NAME_returnPressed(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 6:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAction* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CalibrationChart::*)(std::vector<float> );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CalibrationChart::TransmitCalibrationData)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (CalibrationChart::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CalibrationChart::TransmitCmd)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (CalibrationChart::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CalibrationChart::TransmitTestCmd)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CalibrationChart::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_CalibrationChart.data,
    qt_meta_data_CalibrationChart,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CalibrationChart::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CalibrationChart::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CalibrationChart.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int CalibrationChart::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 23)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 23;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 23)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 23;
    }
    return _id;
}

// SIGNAL 0
void CalibrationChart::TransmitCalibrationData(std::vector<float> _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void CalibrationChart::TransmitCmd(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void CalibrationChart::TransmitTestCmd(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
