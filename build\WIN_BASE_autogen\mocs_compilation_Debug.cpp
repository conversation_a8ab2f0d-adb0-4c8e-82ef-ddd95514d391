// This file is autogenerated. Changes will be overwritten.
#include <EWIEGA46WW/moc_calibration_chart.cpp>
#include <EWIEGA46WW/moc_dynamic_calibration.cpp>
#include <EWIEGA46WW/moc_greymap_chart.cpp>
#include <EWIEGA46WW/moc_histogram_chart.cpp>
#include <EWIEGA46WW/moc_import_pointcloud.cpp>
#include <EWIEGA46WW/moc_lidar_database.cpp>
#include <EWIEGA46WW/moc_lidar_protocol.cpp>
#include <EWIEGA46WW/moc_other_protocol.cpp>
#include <EWIEGA46WW/moc_pointcloud_quality.cpp>
#include <EWIEGA46WW/moc_pointclouds_chart.cpp>
#include <EWIEGA46WW/moc_record_pointcloud.cpp>
#include <EWIEGA46WW/moc_saveas_capture.cpp>
#include <EWIEGA46WW/moc_serial_base.cpp>
#include <EWIEGA46WW/moc_show_image.cpp>
#include <EWIEGA46WW/moc_widget.cpp>
