/****************************************************************************
** Meta object code from reading C++ file 'record_pointcloud.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../record_pointcloud.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'record_pointcloud.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_RecordPointCloud_t {
    QByteArrayData data[7];
    char stringdata0[114];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_RecordPointCloud_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_RecordPointCloud_t qt_meta_stringdata_RecordPointCloud = {
    {
QT_MOC_LITERAL(0, 0, 16), // "RecordPointCloud"
QT_MOC_LITERAL(1, 17, 10), // "StarRecord"
QT_MOC_LITERAL(2, 28, 0), // ""
QT_MOC_LITERAL(3, 29, 8), // "fileName"
QT_MOC_LITERAL(4, 38, 10), // "StopRecord"
QT_MOC_LITERAL(5, 49, 32), // "on_pushButtonStartRecord_clicked"
QT_MOC_LITERAL(6, 82, 31) // "on_pushButtonStopRecord_clicked"

    },
    "RecordPointCloud\0StarRecord\0\0fileName\0"
    "StopRecord\0on_pushButtonStartRecord_clicked\0"
    "on_pushButtonStopRecord_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_RecordPointCloud[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   34,    2, 0x06 /* Public */,
       4,    0,   37,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    0,   38,    2, 0x08 /* Private */,
       6,    0,   39,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void RecordPointCloud::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<RecordPointCloud *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->StarRecord((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 1: _t->StopRecord(); break;
        case 2: _t->on_pushButtonStartRecord_clicked(); break;
        case 3: _t->on_pushButtonStopRecord_clicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (RecordPointCloud::*)(QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&RecordPointCloud::StarRecord)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (RecordPointCloud::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&RecordPointCloud::StopRecord)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject RecordPointCloud::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_RecordPointCloud.data,
    qt_meta_data_RecordPointCloud,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *RecordPointCloud::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *RecordPointCloud::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_RecordPointCloud.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int RecordPointCloud::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void RecordPointCloud::StarRecord(QString _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void RecordPointCloud::StopRecord()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
