/********************************************************************************
** Form generated from reading UI file 'widget.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_WIDGET_H
#define UI_WIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Widget
{
public:
    QAction *action_his;
    QWidget *layoutWidget;
    QHBoxLayout *horizontalLayout;
    QComboBox *comboBox;
    QComboBox *comboBox_baud1;
    QPushButton *pushButton;
    QWidget *layoutWidget1;
    QHBoxLayout *horizontalLayout_2;
    QComboBox *comboBox_2;
    QComboBox *comboBox_baud2;
    QPushButton *pushButton_2;
    QPushButton *pushButton_POINTCLOUD;
    QPushButton *pushButton_GREYMAP;
    QPushButton *pushButton_CALIBRATION;
    QPushButton *pushButton_HISTONGRAM;
    QPushButton *pushButton_DATABASE;

    void setupUi(QWidget *Widget)
    {
        if (Widget->objectName().isEmpty())
            Widget->setObjectName(QString::fromUtf8("Widget"));
        Widget->resize(459, 202);
        Widget->setMinimumSize(QSize(459, 202));
        Widget->setAutoFillBackground(false);
        Widget->setStyleSheet(QString::fromUtf8("*{\n"
"	font: 75 12pt \"Agency FB\";\n"
"}\n"
"\n"
"QLabel\n"
"{ \n"
"	/*font: 25 10pt \"Microsoft YaHei\";  */\n"
"	border-radius: 10px;	\n"
"}\n"
"\n"
"QLineEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"QTextEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"/*\346\214\211\351\222\256\351\235\231\346\255\242\346\227\240\346\223\215\344\275\234\346\240\267\345\274\217*/\n"
"QPushButton \n"
"{\n"
"    /*background-color:blue;*/\n"
"    /*background-color: rgba(0, 102, 116, 240); \n"
"    color:white; */\n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt; */\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
" \n"
"/*\351\274\240\346\240\207\346\202\254\345\201\234\345\234\250\346\214\211\351\222\256*/\n"
"QPushButton:hover\n"
"{\n"
"    background-color: gray; \n"
"    color:rgb(6,168,255);\n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351"
                        "\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QPushButton:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"\n"
"/* === QGroupBox === */\n"
"QGroupBox {\n"
"    border: 2px solid gray;\n"
"    margin-top: 2ex;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    color: rgba(0, 102, 116, 240);\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    margin-left: 5px;\n"
"}\n"
"\n"
"\n"
"/* === QRadioButton === */\n"
"QRadioButton {\n"
"	/*background-color: rgba(0, 102, 116, 240); */\n"
"	/*color: white; */\n"
"    border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
"/* === QComboBox === */\n"
"QComboBox \n"
"{\n"
"	color:rgb(0,0,0);\n"
"	/*background:rgba(0, 102, 116, 240);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:hover\n"
"{\n"
"    color:gray;\n"
"	/*background:rgba(0, 102, 116, 240);*/\n"
"    border: 2px solid gr"
                        "ay;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:pressed\n"
"{\n"
"    /*color:rgb(6,168,255);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:on\n"
"{\n"
"    /*color:rgb(6,168,255)*/\n"
"	/*color:rgb(255,255,255);*/;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item:selected\n"
"{	\n"
"	color:rgb(255,255,255);\n"
"	background-color:gray;\n"
"}\n"
"\n"
"\n"
"/********QGroupBox*******/\n"
"\n"
"QGroupBox \n"
"{\n"
"	color:rgb(6,168,255);\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"/********QToolBox*******/\n"
"QToolBox::tab {\n"
"    color: white;\n"
"	background-color: rgba(0, 102, 116, 240);\n"
"	font: 25 18pt \"Agency FB\";\n"
"  "
                        "  border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"QToolBoxButton \n"
"{\n"
"    min-height: 40px; \n"
"	text-align: right;\n"
"}\n"
"\n"
"QToolBox::tab:hover\n"
"{\n"
"    color: gray;\n"
"	background-color: rgb(0, 170, 127);\n"
"	font: 25 18pt \"Agency FB\"; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QToolBox::tab:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"/*********/\n"
" \n"
"\n"
""));
        action_his = new QAction(Widget);
        action_his->setObjectName(QString::fromUtf8("action_his"));
        layoutWidget = new QWidget(Widget);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(20, 140, 421, 51));
        horizontalLayout = new QHBoxLayout(layoutWidget);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        comboBox = new QComboBox(layoutWidget);
        comboBox->setObjectName(QString::fromUtf8("comboBox"));
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(comboBox->sizePolicy().hasHeightForWidth());
        comboBox->setSizePolicy(sizePolicy);

        horizontalLayout->addWidget(comboBox);

        comboBox_baud1 = new QComboBox(layoutWidget);
        comboBox_baud1->setObjectName(QString::fromUtf8("comboBox_baud1"));
        sizePolicy.setHeightForWidth(comboBox_baud1->sizePolicy().hasHeightForWidth());
        comboBox_baud1->setSizePolicy(sizePolicy);
        comboBox_baud1->setEditable(true);

        horizontalLayout->addWidget(comboBox_baud1);

        pushButton = new QPushButton(layoutWidget);
        pushButton->setObjectName(QString::fromUtf8("pushButton"));
        QSizePolicy sizePolicy1(QSizePolicy::Minimum, QSizePolicy::Preferred);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(pushButton->sizePolicy().hasHeightForWidth());
        pushButton->setSizePolicy(sizePolicy1);

        horizontalLayout->addWidget(pushButton);

        layoutWidget1 = new QWidget(Widget);
        layoutWidget1->setObjectName(QString::fromUtf8("layoutWidget1"));
        layoutWidget1->setGeometry(QRect(20, 210, 421, 51));
        horizontalLayout_2 = new QHBoxLayout(layoutWidget1);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        comboBox_2 = new QComboBox(layoutWidget1);
        comboBox_2->setObjectName(QString::fromUtf8("comboBox_2"));
        sizePolicy.setHeightForWidth(comboBox_2->sizePolicy().hasHeightForWidth());
        comboBox_2->setSizePolicy(sizePolicy);

        horizontalLayout_2->addWidget(comboBox_2);

        comboBox_baud2 = new QComboBox(layoutWidget1);
        comboBox_baud2->setObjectName(QString::fromUtf8("comboBox_baud2"));
        sizePolicy.setHeightForWidth(comboBox_baud2->sizePolicy().hasHeightForWidth());
        comboBox_baud2->setSizePolicy(sizePolicy);

        horizontalLayout_2->addWidget(comboBox_baud2);

        pushButton_2 = new QPushButton(layoutWidget1);
        pushButton_2->setObjectName(QString::fromUtf8("pushButton_2"));
        sizePolicy1.setHeightForWidth(pushButton_2->sizePolicy().hasHeightForWidth());
        pushButton_2->setSizePolicy(sizePolicy1);

        horizontalLayout_2->addWidget(pushButton_2);

        pushButton_POINTCLOUD = new QPushButton(Widget);
        pushButton_POINTCLOUD->setObjectName(QString::fromUtf8("pushButton_POINTCLOUD"));
        pushButton_POINTCLOUD->setGeometry(QRect(21, 31, 80, 80));
        sizePolicy.setHeightForWidth(pushButton_POINTCLOUD->sizePolicy().hasHeightForWidth());
        pushButton_POINTCLOUD->setSizePolicy(sizePolicy);
        pushButton_POINTCLOUD->setStyleSheet(QString::fromUtf8("border-image: url(:/new/prefix1/icon/logo/lidar.png);"));
        pushButton_GREYMAP = new QPushButton(Widget);
        pushButton_GREYMAP->setObjectName(QString::fromUtf8("pushButton_GREYMAP"));
        pushButton_GREYMAP->setGeometry(QRect(272, 31, 80, 80));
        sizePolicy.setHeightForWidth(pushButton_GREYMAP->sizePolicy().hasHeightForWidth());
        pushButton_GREYMAP->setSizePolicy(sizePolicy);
        pushButton_GREYMAP->setStyleSheet(QString::fromUtf8("border-image: url(:/new/prefix1/icon/logo/greymap2.png);"));
        pushButton_CALIBRATION = new QPushButton(Widget);
        pushButton_CALIBRATION->setObjectName(QString::fromUtf8("pushButton_CALIBRATION"));
        pushButton_CALIBRATION->setGeometry(QRect(356, 31, 80, 80));
        sizePolicy.setHeightForWidth(pushButton_CALIBRATION->sizePolicy().hasHeightForWidth());
        pushButton_CALIBRATION->setSizePolicy(sizePolicy);
        pushButton_CALIBRATION->setStyleSheet(QString::fromUtf8("border-image: url(:/new/prefix1/icon/logo/fit2.png);"));
        pushButton_HISTONGRAM = new QPushButton(Widget);
        pushButton_HISTONGRAM->setObjectName(QString::fromUtf8("pushButton_HISTONGRAM"));
        pushButton_HISTONGRAM->setGeometry(QRect(189, 31, 80, 80));
        sizePolicy.setHeightForWidth(pushButton_HISTONGRAM->sizePolicy().hasHeightForWidth());
        pushButton_HISTONGRAM->setSizePolicy(sizePolicy);
        pushButton_HISTONGRAM->setAutoFillBackground(false);
        pushButton_HISTONGRAM->setStyleSheet(QString::fromUtf8("border-image: url(:/new/prefix1/icon/logo/histogram.png);"));
        pushButton_HISTONGRAM->setIconSize(QSize(99, 69));
        pushButton_HISTONGRAM->setAutoRepeat(false);
        pushButton_HISTONGRAM->setAutoExclusive(false);
        pushButton_HISTONGRAM->setAutoDefault(false);
        pushButton_HISTONGRAM->setFlat(false);
        pushButton_DATABASE = new QPushButton(Widget);
        pushButton_DATABASE->setObjectName(QString::fromUtf8("pushButton_DATABASE"));
        pushButton_DATABASE->setGeometry(QRect(105, 31, 80, 80));
        sizePolicy.setHeightForWidth(pushButton_DATABASE->sizePolicy().hasHeightForWidth());
        pushButton_DATABASE->setSizePolicy(sizePolicy);
        pushButton_DATABASE->setStyleSheet(QString::fromUtf8("border-image: url(:/new/prefix1/icon/logo/database.png);"));

        retranslateUi(Widget);

        QMetaObject::connectSlotsByName(Widget);
    } // setupUi

    void retranslateUi(QWidget *Widget)
    {
        Widget->setWindowTitle(QString());
        action_his->setText(QCoreApplication::translate("Widget", "\347\233\264\346\226\271\345\233\276", nullptr));
        pushButton->setText(QCoreApplication::translate("Widget", "open", nullptr));
        pushButton_2->setText(QCoreApplication::translate("Widget", "open", nullptr));
        pushButton_POINTCLOUD->setText(QString());
        pushButton_GREYMAP->setText(QString());
        pushButton_CALIBRATION->setText(QString());
        pushButton_HISTONGRAM->setText(QString());
        pushButton_DATABASE->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class Widget: public Ui_Widget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_WIDGET_H
