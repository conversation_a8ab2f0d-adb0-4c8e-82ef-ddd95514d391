﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>WIN_BASE</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">WIN_BASE.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">WIN_BASE-V2_3_11_T5</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">WIN_BASE.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">WIN_BASE-V2_3_11_T5</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">WIN_BASE.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">WIN_BASE-V2_3_11_T5</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">WIN_BASE.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">WIN_BASE-V2_3_11_T5</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION="2.3.11";QT_CORE_LIB;QT_GUI_LIB;QT_WIDGETS_LIB;QT_SERIALPORT_LIB;QT_PRINTSUPPORT_LIB;QT_AXCONTAINER_LIB;QT_AXBASE_LIB;QT_SQL_LIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION=\"2.3.11\";QT_CORE_LIB;QT_GUI_LIB;QT_WIDGETS_LIB;QT_SERIALPORT_LIB;QT_PRINTSUPPORT_LIB;QT_AXCONTAINER_LIB;QT_AXBASE_LIB;QT_SQL_LIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target WIN_BASE</Message>
      <Command>setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autogen F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E touch F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/autouic_Debug.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5SerialPort.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5AxContainer.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Sql.a;..\libdToF_calibration.a;polynomialFit\Debug\polynomialFit.lib;customPlot\Debug\customPlot.lib;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5AxBase.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5AxBase.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Widgets.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Gui.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Core.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5PrintSupport.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Widgets.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Gui.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Core.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libqtmain.a;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/Debug/WIN_BASE-V2_3_11_T5.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/Debug/WIN_BASE-V2_3_11_T5.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION="2.3.11";QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_WIDGETS_LIB;QT_SERIALPORT_LIB;QT_PRINTSUPPORT_LIB;QT_AXCONTAINER_LIB;QT_AXBASE_LIB;QT_SQL_LIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION=\"2.3.11\";QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_WIDGETS_LIB;QT_SERIALPORT_LIB;QT_PRINTSUPPORT_LIB;QT_AXCONTAINER_LIB;QT_AXBASE_LIB;QT_SQL_LIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target WIN_BASE</Message>
      <Command>setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autogen F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E touch F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/autouic_Release.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5SerialPort.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5AxContainer.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Sql.a;..\libdToF_calibration.a;polynomialFit\Release\polynomialFit.lib;customPlot\Release\customPlot.lib;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5AxBase.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5AxBase.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Widgets.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Gui.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Core.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5PrintSupport.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Widgets.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Gui.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Core.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libqtmain.a;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/Release/WIN_BASE-V2_3_11_T5.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/Release/WIN_BASE-V2_3_11_T5.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION="2.3.11";QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_WIDGETS_LIB;QT_SERIALPORT_LIB;QT_PRINTSUPPORT_LIB;QT_AXCONTAINER_LIB;QT_AXBASE_LIB;QT_SQL_LIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION=\"2.3.11\";QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_WIDGETS_LIB;QT_SERIALPORT_LIB;QT_PRINTSUPPORT_LIB;QT_AXCONTAINER_LIB;QT_AXBASE_LIB;QT_SQL_LIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target WIN_BASE</Message>
      <Command>setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autogen F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E touch F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/autouic_MinSizeRel.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5SerialPort.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5AxContainer.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Sql.a;..\libdToF_calibration.a;polynomialFit\MinSizeRel\polynomialFit.lib;customPlot\MinSizeRel\customPlot.lib;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5AxBase.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5AxBase.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Widgets.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Gui.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Core.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5PrintSupport.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Widgets.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Gui.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Core.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libqtmain.a;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/MinSizeRel/WIN_BASE-V2_3_11_T5.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/MinSizeRel/WIN_BASE-V2_3_11_T5.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION="2.3.11";QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_WIDGETS_LIB;QT_SERIALPORT_LIB;QT_PRINTSUPPORT_LIB;QT_AXCONTAINER_LIB;QT_AXBASE_LIB;QT_SQL_LIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION=\"2.3.11\";QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_WIDGETS_LIB;QT_SERIALPORT_LIB;QT_PRINTSUPPORT_LIB;QT_AXCONTAINER_LIB;QT_AXBASE_LIB;QT_SQL_LIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtPrintSupport;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\ActiveQt;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target WIN_BASE</Message>
      <Command>setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autogen F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E touch F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/WIN_BASE_autogen/autouic_RelWithDebInfo.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5SerialPort.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5AxContainer.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Sql.a;..\libdToF_calibration.a;polynomialFit\RelWithDebInfo\polynomialFit.lib;customPlot\RelWithDebInfo\customPlot.lib;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5AxBase.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5AxBase.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Widgets.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Gui.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\\libQt5Core.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5PrintSupport.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Widgets.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Gui.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libQt5Core.a;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\libqtmain.a;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/RelWithDebInfo/WIN_BASE-V2_3_11_T5.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/RelWithDebInfo/WIN_BASE-V2_3_11_T5.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\b757634ceedbf6ab2645d3fff98d5cb3\autouic_(CONFIG).stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\widget.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dynamiccalibration.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudquality.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\showimage.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\importpointcloud.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\recordpointcloud.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\saveascapture.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\calibrationchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\greymapchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\histogramchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudschart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidardatabase.ui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_Debug.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\widget.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dynamiccalibration.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudquality.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\showimage.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\importpointcloud.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\recordpointcloud.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\saveascapture.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\calibrationchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\greymapchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\histogramchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudschart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidardatabase.ui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_Release.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\widget.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dynamiccalibration.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudquality.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\showimage.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\importpointcloud.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\recordpointcloud.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\saveascapture.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\calibrationchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\greymapchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\histogramchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudschart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidardatabase.ui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_MinSizeRel.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\widget.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dynamiccalibration.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudquality.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\showimage.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\importpointcloud.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\recordpointcloud.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\saveascapture.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\calibrationchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\greymapchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\histogramchart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudschart.ui;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidardatabase.ui;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_RelWithDebInfo.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\2f1de9a012f3b2153bcbe432347b727f\qrc_logo.cpp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Automatic RCC for logo.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_logo_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\stop.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\running.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\record.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\calibration.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\histogram.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\greymap.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\shot.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\running2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\import.jpeg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\save.jpeg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\scan.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\stop2.png;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Automatic RCC for logo.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_logo_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\stop.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\running.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\record.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\calibration.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\histogram.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\greymap.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\shot.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\running2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\import.jpeg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\save.jpeg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\scan.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\stop2.png;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Automatic RCC for logo.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_logo_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\stop.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\running.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\record.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\calibration.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\histogram.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\greymap.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\shot.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\running2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\import.jpeg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\save.jpeg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\scan.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\stop2.png;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Automatic RCC for logo.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_logo_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\stop.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\running.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\record.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\calibration.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\histogram.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\greymap.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\shot.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\running2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\import.jpeg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\save.jpeg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\scan.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\stop2.png;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\2f1de9a012f3b2153bcbe432347b727f\qrc_logo2.cpp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Automatic RCC for logo2.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_logo2_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\quick.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\read.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\download.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\email2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\write.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\running.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\database.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\revert.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\greymap2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\share.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\slow.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\fit2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\revertAll.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\download2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\home.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\lidar.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\insert.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\greymap.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\reveret1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\list.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\insertImage.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\import.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\yumao.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan5.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\email.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\magnifier.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\sacn.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\refresh.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\fit.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\delete.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\histogram.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\sacn4.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\searchOnce.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\cat.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\copy.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\serachAll.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\stop.png;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo2.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Automatic RCC for logo2.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_logo2_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\quick.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\read.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\download.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\email2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\write.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\running.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\database.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\revert.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\greymap2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\share.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\slow.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\fit2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\revertAll.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\download2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\home.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\lidar.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\insert.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\greymap.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\reveret1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\list.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\insertImage.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\import.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\yumao.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan5.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\email.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\magnifier.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\sacn.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\refresh.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\fit.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\delete.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\histogram.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\sacn4.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\searchOnce.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\cat.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\copy.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\serachAll.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\stop.png;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo2.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Automatic RCC for logo2.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_logo2_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\quick.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\read.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\download.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\email2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\write.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\running.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\database.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\revert.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\greymap2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\share.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\slow.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\fit2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\revertAll.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\download2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\home.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\lidar.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\insert.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\greymap.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\reveret1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\list.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\insertImage.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\import.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\yumao.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan5.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\email.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\magnifier.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\sacn.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\refresh.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\fit.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\delete.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\histogram.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\sacn4.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\searchOnce.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\cat.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\copy.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\serachAll.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\stop.png;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo2.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Automatic RCC for logo2.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_logo2_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_logo2_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\quick.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\read.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\download.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\email2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\write.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\running.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\database.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\revert.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\greymap2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\share.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\slow.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\fit2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\revertAll.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\download2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\home.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\lidar.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\insert.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\greymap.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\reveret1.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\list.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\insertImage.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\import.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\yumao.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan5.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\email.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\magnifier.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\sacn.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\refresh.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\fit.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\delete.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\histogram.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\sacn4.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\scan3.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\shot.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\searchOnce.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\cat.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\record.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\copy.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\serachAll.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\calibration2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\upLoad2.png;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\logo\stop.png;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo2.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\2f1de9a012f3b2153bcbe432347b727f\qrc_res.cpp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Automatic RCC for res.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_res_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\cspc1.jpg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\cspc.jpg;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_res.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Automatic RCC for res.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_res_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\cspc1.jpg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\cspc.jpg;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_res.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Automatic RCC for res.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_res_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\cspc1.jpg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\cspc.jpg;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_res.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Automatic RCC for res.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/WIN_BASE_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\WIN_BASE_autogen.dir\AutoRcc_res_EWIEGA46WW_Info.json;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\cspc1.jpg;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\icon\cspc.jpg;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_res.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCXXInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCommonLanguageInclude.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeGenericSystem.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeInitializeConfigs.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeLanguageInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeParseArguments.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeRCInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInitialize.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\WindowsPaths.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeCXXCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeRCCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeSystem.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCXXInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCommonLanguageInclude.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeGenericSystem.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeInitializeConfigs.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeLanguageInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeParseArguments.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeRCInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInitialize.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\WindowsPaths.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeCXXCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeRCCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeSystem.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCXXInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCommonLanguageInclude.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeGenericSystem.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeInitializeConfigs.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeLanguageInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeParseArguments.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeRCInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInitialize.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\WindowsPaths.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeCXXCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeRCCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeSystem.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCXXInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeCommonLanguageInclude.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeGenericSystem.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeInitializeConfigs.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeLanguageInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeParseArguments.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeRCInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInformation.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\CMakeSystemSpecificInitialize.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Compiler\MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows-MSVC.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\Windows.cmake;D:\Programs\CMake\share\cmake-3.21\Modules\Platform\WindowsPaths.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxBase\Qt5AxBaseConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5AxContainer\Qt5AxContainerConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupportConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5PrintSupport\Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5SerialPort\Qt5SerialPortConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeCXXCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeRCCompiler.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\3.21.2\CMakeSystem.cmake;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\main.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\widget.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dynamic_calibration.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\import_pointcloud.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloud_quality.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\show_image.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidar_protocol.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\other_protocol.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\record_pointcloud.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\saveas_capture.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\serial_base.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\calibration_chart.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\greymap_chart.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\histogram_chart.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointclouds_chart.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidar_database.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\task_process\task_base.cpp" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\widget.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dynamic_calibration.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\import_pointcloud.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidar_protocol.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\other_protocol.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloud_quality.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\record_pointcloud.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\saveas_capture.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\serial_base.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\calibration_chart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\greymap_chart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\histogram_chart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointclouds_chart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidar_database.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\show_image.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\task_process\task_base.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dtof_calibration.h" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\widget.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dynamiccalibration.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudquality.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\showimage.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\importpointcloud.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\recordpointcloud.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\saveascapture.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\calibrationchart.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\greymapchart.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\histogramchart.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudschart.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidardatabase.ui">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc">
    </None>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc">
    </None>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_widget.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_dynamiccalibration.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_pointcloudquality.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_showimage.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_importpointcloud.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_recordpointcloud.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_saveascapture.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_calibrationchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_greymapchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_histogramchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_pointcloudschart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_lidardatabase.h" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_Debug.stamp">
    </None>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo2.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_res.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_widget.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_dynamiccalibration.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_pointcloudquality.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_showimage.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_importpointcloud.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_recordpointcloud.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_saveascapture.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_calibrationchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_greymapchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_histogramchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_pointcloudschart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_lidardatabase.h" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_Release.stamp">
    </None>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_widget.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_dynamiccalibration.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_pointcloudquality.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_showimage.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_importpointcloud.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_recordpointcloud.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_saveascapture.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_calibrationchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_greymapchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_histogramchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_pointcloudschart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_lidardatabase.h" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_MinSizeRel.stamp">
    </None>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_widget.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_dynamiccalibration.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_pointcloudquality.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_showimage.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_importpointcloud.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_recordpointcloud.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_saveascapture.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_calibrationchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_greymapchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_histogramchart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_pointcloudschart.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_lidardatabase.h" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_RelWithDebInfo.stamp">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\ZERO_CHECK.vcxproj">
      <Project>{3A64A487-E8B2-3202-8B19-F249F4C60E24}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\customPlot\customPlot.vcxproj">
      <Project>{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}</Project>
      <Name>customPlot</Name>
    </ProjectReference>
    <ProjectReference Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit.vcxproj">
      <Project>{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}</Project>
      <Name>polynomialFit</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>