﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{B666D4E4-26C7-30B6-9DC6-8827B4D20734}"
	ProjectSection(ProjectDependencies) = postProject
		{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21} = {FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}
		{3A64A487-E8B2-3202-8B19-F249F4C60E24} = {3A64A487-E8B2-3202-8B19-F249F4C60E24}
		{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC} = {77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}
		{C8FD8C9D-CC26-3048-ADEE-A40A724774CB} = {C8FD8C9D-CC26-3048-ADEE-A40A724774CB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{2CE19BC4-ACA4-3883-8DF8-A9224927CC89}"
	ProjectSection(ProjectDependencies) = postProject
		{B666D4E4-26C7-30B6-9DC6-8827B4D20734} = {B666D4E4-26C7-30B6-9DC6-8827B4D20734}
		{3A64A487-E8B2-3202-8B19-F249F4C60E24} = {3A64A487-E8B2-3202-8B19-F249F4C60E24}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "WIN_BASE", "WIN_BASE.vcxproj", "{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}"
	ProjectSection(ProjectDependencies) = postProject
		{3A64A487-E8B2-3202-8B19-F249F4C60E24} = {3A64A487-E8B2-3202-8B19-F249F4C60E24}
		{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC} = {77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}
		{C8FD8C9D-CC26-3048-ADEE-A40A724774CB} = {C8FD8C9D-CC26-3048-ADEE-A40A724774CB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{3A64A487-E8B2-3202-8B19-F249F4C60E24}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "customPlot", "customPlot\customPlot.vcxproj", "{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}"
	ProjectSection(ProjectDependencies) = postProject
		{3A64A487-E8B2-3202-8B19-F249F4C60E24} = {3A64A487-E8B2-3202-8B19-F249F4C60E24}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "polynomialFit", "polynomialFit\polynomialFit.vcxproj", "{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}"
	ProjectSection(ProjectDependencies) = postProject
		{3A64A487-E8B2-3202-8B19-F249F4C60E24} = {3A64A487-E8B2-3202-8B19-F249F4C60E24}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B666D4E4-26C7-30B6-9DC6-8827B4D20734}.Debug|x64.ActiveCfg = Debug|x64
		{B666D4E4-26C7-30B6-9DC6-8827B4D20734}.Debug|x64.Build.0 = Debug|x64
		{B666D4E4-26C7-30B6-9DC6-8827B4D20734}.Release|x64.ActiveCfg = Release|x64
		{B666D4E4-26C7-30B6-9DC6-8827B4D20734}.Release|x64.Build.0 = Release|x64
		{B666D4E4-26C7-30B6-9DC6-8827B4D20734}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B666D4E4-26C7-30B6-9DC6-8827B4D20734}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B666D4E4-26C7-30B6-9DC6-8827B4D20734}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B666D4E4-26C7-30B6-9DC6-8827B4D20734}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{2CE19BC4-ACA4-3883-8DF8-A9224927CC89}.Debug|x64.ActiveCfg = Debug|x64
		{2CE19BC4-ACA4-3883-8DF8-A9224927CC89}.Release|x64.ActiveCfg = Release|x64
		{2CE19BC4-ACA4-3883-8DF8-A9224927CC89}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2CE19BC4-ACA4-3883-8DF8-A9224927CC89}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}.Debug|x64.ActiveCfg = Debug|x64
		{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}.Debug|x64.Build.0 = Debug|x64
		{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}.Release|x64.ActiveCfg = Release|x64
		{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}.Release|x64.Build.0 = Release|x64
		{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FE3FAF7C-EDF4-32C1-9ABE-616DC93D1C21}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{3A64A487-E8B2-3202-8B19-F249F4C60E24}.Debug|x64.ActiveCfg = Debug|x64
		{3A64A487-E8B2-3202-8B19-F249F4C60E24}.Debug|x64.Build.0 = Debug|x64
		{3A64A487-E8B2-3202-8B19-F249F4C60E24}.Release|x64.ActiveCfg = Release|x64
		{3A64A487-E8B2-3202-8B19-F249F4C60E24}.Release|x64.Build.0 = Release|x64
		{3A64A487-E8B2-3202-8B19-F249F4C60E24}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{3A64A487-E8B2-3202-8B19-F249F4C60E24}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{3A64A487-E8B2-3202-8B19-F249F4C60E24}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{3A64A487-E8B2-3202-8B19-F249F4C60E24}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}.Debug|x64.ActiveCfg = Debug|x64
		{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}.Debug|x64.Build.0 = Debug|x64
		{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}.Release|x64.ActiveCfg = Release|x64
		{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}.Release|x64.Build.0 = Release|x64
		{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{77EBB1C1-92B8-3551-8D81-4DB7FD2E10FC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}.Debug|x64.ActiveCfg = Debug|x64
		{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}.Debug|x64.Build.0 = Debug|x64
		{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}.Release|x64.ActiveCfg = Release|x64
		{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}.Release|x64.Build.0 = Release|x64
		{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1799D00B-63A6-3B95-BC7A-D2BE8BDCAC14}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
