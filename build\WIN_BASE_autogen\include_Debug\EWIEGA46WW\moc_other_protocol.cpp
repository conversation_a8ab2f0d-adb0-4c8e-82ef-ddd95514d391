/****************************************************************************
** Meta object code from reading C++ file 'other_protocol.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../other_protocol.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'other_protocol.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_otherdevice__ProcessProtocol_t {
    QByteArrayData data[22];
    char stringdata0[304];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_otherdevice__ProcessProtocol_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_otherdevice__ProcessProtocol_t qt_meta_stringdata_otherdevice__ProcessProtocol = {
    {
QT_MOC_LITERAL(0, 0, 28), // "otherdevice::ProcessProtocol"
QT_MOC_LITERAL(1, 29, 27), // "ProcessSendToMainPointCloud"
QT_MOC_LITERAL(2, 57, 0), // ""
QT_MOC_LITERAL(3, 58, 25), // "std::vector<ProtocolData>"
QT_MOC_LITERAL(4, 84, 4), // "data"
QT_MOC_LITERAL(5, 89, 12), // "FeedbackInfo"
QT_MOC_LITERAL(6, 102, 3), // "fdb"
QT_MOC_LITERAL(7, 106, 21), // "transmitHistogramData"
QT_MOC_LITERAL(8, 128, 2), // "tr"
QT_MOC_LITERAL(9, 131, 19), // "transmitGreymapData"
QT_MOC_LITERAL(10, 151, 23), // "transmitCalibrationData"
QT_MOC_LITERAL(11, 175, 22), // "transmitPointCloudData"
QT_MOC_LITERAL(12, 198, 19), // "TransmitTemperature"
QT_MOC_LITERAL(13, 218, 4), // "temp"
QT_MOC_LITERAL(14, 223, 10), // "QuitThread"
QT_MOC_LITERAL(15, 234, 7), // "isClose"
QT_MOC_LITERAL(16, 242, 12), // "TransmitData"
QT_MOC_LITERAL(17, 255, 3), // "str"
QT_MOC_LITERAL(18, 259, 15), // "SetProtocolType"
QT_MOC_LITERAL(19, 275, 12), // "protocolType"
QT_MOC_LITERAL(20, 288, 10), // "RecordData"
QT_MOC_LITERAL(21, 299, 4) // "isOk"

    },
    "otherdevice::ProcessProtocol\0"
    "ProcessSendToMainPointCloud\0\0"
    "std::vector<ProtocolData>\0data\0"
    "FeedbackInfo\0fdb\0transmitHistogramData\0"
    "tr\0transmitGreymapData\0transmitCalibrationData\0"
    "transmitPointCloudData\0TransmitTemperature\0"
    "temp\0QuitThread\0isClose\0TransmitData\0"
    "str\0SetProtocolType\0protocolType\0"
    "RecordData\0isOk"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_otherdevice__ProcessProtocol[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       7,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   69,    2, 0x06 /* Public */,
       5,    1,   72,    2, 0x06 /* Public */,
       7,    1,   75,    2, 0x06 /* Public */,
       9,    1,   78,    2, 0x06 /* Public */,
      10,    1,   81,    2, 0x06 /* Public */,
      11,    1,   84,    2, 0x06 /* Public */,
      12,    1,   87,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      14,    1,   90,    2, 0x0a /* Public */,
      16,    1,   93,    2, 0x0a /* Public */,
      18,    1,   96,    2, 0x0a /* Public */,
      20,    1,   99,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::QByteArray,    6,
    QMetaType::Void, QMetaType::QByteArray,    8,
    QMetaType::Void, QMetaType::QByteArray,    8,
    QMetaType::Void, QMetaType::QByteArray,    8,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::Float,   13,

 // slots: parameters
    QMetaType::Void, QMetaType::Bool,   15,
    QMetaType::Void, QMetaType::QByteArray,   17,
    QMetaType::Void, QMetaType::Int,   19,
    QMetaType::Void, QMetaType::Bool,   21,

       0        // eod
};

void otherdevice::ProcessProtocol::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ProcessProtocol *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->ProcessSendToMainPointCloud((*reinterpret_cast< std::vector<ProtocolData>(*)>(_a[1]))); break;
        case 1: _t->FeedbackInfo((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 2: _t->transmitHistogramData((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 3: _t->transmitGreymapData((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 4: _t->transmitCalibrationData((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 5: _t->transmitPointCloudData((*reinterpret_cast< std::vector<ProtocolData>(*)>(_a[1]))); break;
        case 6: _t->TransmitTemperature((*reinterpret_cast< float(*)>(_a[1]))); break;
        case 7: _t->QuitThread((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 8: _t->TransmitData((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 9: _t->SetProtocolType((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 10: _t->RecordData((*reinterpret_cast< bool(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ProcessProtocol::*)(std::vector<ProtocolData> );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::ProcessSendToMainPointCloud)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::FeedbackInfo)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::transmitHistogramData)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::transmitGreymapData)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::transmitCalibrationData)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(std::vector<ProtocolData> );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::transmitPointCloudData)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(float );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::TransmitTemperature)) {
                *result = 6;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject otherdevice::ProcessProtocol::staticMetaObject = { {
    QMetaObject::SuperData::link<SerialBase::staticMetaObject>(),
    qt_meta_stringdata_otherdevice__ProcessProtocol.data,
    qt_meta_data_otherdevice__ProcessProtocol,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *otherdevice::ProcessProtocol::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *otherdevice::ProcessProtocol::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_otherdevice__ProcessProtocol.stringdata0))
        return static_cast<void*>(this);
    return SerialBase::qt_metacast(_clname);
}

int otherdevice::ProcessProtocol::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = SerialBase::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 11;
    }
    return _id;
}

// SIGNAL 0
void otherdevice::ProcessProtocol::ProcessSendToMainPointCloud(std::vector<ProtocolData> _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void otherdevice::ProcessProtocol::FeedbackInfo(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void otherdevice::ProcessProtocol::transmitHistogramData(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void otherdevice::ProcessProtocol::transmitGreymapData(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void otherdevice::ProcessProtocol::transmitCalibrationData(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void otherdevice::ProcessProtocol::transmitPointCloudData(std::vector<ProtocolData> _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void otherdevice::ProcessProtocol::TransmitTemperature(float _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
