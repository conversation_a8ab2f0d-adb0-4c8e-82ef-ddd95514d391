﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{C8FD8C9D-CC26-3048-ADEE-A40A724774CB}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>polynomialFit</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">polynomialFit.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">polynomialFit</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">polynomialFit.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">polynomialFit</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">polynomialFit.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">polynomialFit</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">polynomialFit.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">polynomialFit</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_Debug;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION="2.3.11";CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION=\"2.3.11\";CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_Debug;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_Debug;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target polynomialFit</Message>
      <Command>setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autogen F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/polynomialFit/CMakeFiles/polynomialFit_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_Release;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION="2.3.11";CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION=\"2.3.11\";CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_Release;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_Release;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target polynomialFit</Message>
      <Command>setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autogen F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/polynomialFit/CMakeFiles/polynomialFit_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_MinSizeRel;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION="2.3.11";CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION=\"2.3.11\";CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_MinSizeRel;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_MinSizeRel;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target polynomialFit</Message>
      <Command>setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autogen F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/polynomialFit/CMakeFiles/polynomialFit_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_RelWithDebInfo;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION="2.3.11";CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_DEPRECATED_WARNINGS;QT_MESSAGELOGCONTEXT;USING_LOG_TO_FILE=0;USING_CUSTOM_STYLE=0;USING_CUSTOM_HIDE=0;APP_VERSION=\"2.3.11\";CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_RelWithDebInfo;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\include_RelWithDebInfo;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\customPlot;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\eigen-3.4.0;F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dll;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target polynomialFit</Message>
      <Command>setlocal
cd F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit
if %errorlevel% neq 0 goto :cmEnd
F:
if %errorlevel% neq 0 goto :cmEnd
D:\Programs\CMake\bin\cmake.exe -E cmake_autogen F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/polynomialFit/CMakeFiles/polynomialFit_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/polynomialFit/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/polynomialFit/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/polynomialFit/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/polynomialFit/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/polynomialFit/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/polynomialFit/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/polynomialFit/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\Programs\CMake\bin\cmake.exe -SF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908 -BF:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build --check-stamp-file F:/13_Yapha-Laser-DTof2dMS/development/tool/winbase_app_0908/build/polynomialFit/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\augmentedMatrix.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\linearFit.cpp" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\polynomialFit.cpp" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\augmentedMatrix.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\linearFit.h" />
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\polynomialFit\polynomialFit.h" />
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\polynomialFit\polynomialFit_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\ZERO_CHECK.vcxproj">
      <Project>{3A64A487-E8B2-3202-8B19-F249F4C60E24}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>