/********************************************************************************
** Form generated from reading UI file 'calibrationchart.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CALIBRATIONCHART_H
#define UI_CALIBRATIONCHART_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QTableView>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "qcustomplot.h"

QT_BEGIN_NAMESPACE

class Ui_CalibrationChart
{
public:
    QWidget *centralwidget;
    QGridLayout *gridLayout_2;
    QHBoxLayout *horizontalLayout_4;
    QVBoxLayout *verticalLayout_3;
    QCustomPlot *widgetCalibration;
    QHBoxLayout *horizontalLayout_3;
    QTextEdit *textEdit_TOF_PEAK;
    QVBoxLayout *verticalLayout_2;
    QPushButton *pushButton_SAVE_LOG;
    QPushButton *pushButton_CLEAR_LOG;
    QLineEdit *lineEdit_FILE_NAME;
    QVBoxLayout *verticalLayout_4;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout;
    QPushButton *stopCMD;
    QPushButton *resetCMD;
    QHBoxLayout *horizontalLayout_2;
    QLabel *label;
    QLineEdit *offset;
    QGroupBox *groupBox;
    QWidget *layoutWidget;
    QHBoxLayout *horizontalLayout_5;
    QVBoxLayout *verticalLayout_5;
    QLabel *label_2;
    QLineEdit *settingDistance;
    QLabel *label_3;
    QLineEdit *settingSpeed;
    QPushButton *RunDis;
    QGroupBox *groupBox_2;
    QTableView *tableView;
    QPushButton *exeRun;
    QWidget *layoutWidget1;
    QGridLayout *gridLayout;
    QLabel *label_4;
    QLabel *label_5;
    QLineEdit *exeNum;
    QLineEdit *saveFileName;
    QWidget *layoutWidget2;
    QHBoxLayout *horizontalLayout_6;
    QLabel *label_6;
    QLineEdit *samplePointDis;
    QPushButton *addSamplePoint;

    void setupUi(QMainWindow *CalibrationChart)
    {
        if (CalibrationChart->objectName().isEmpty())
            CalibrationChart->setObjectName(QString::fromUtf8("CalibrationChart"));
        CalibrationChart->resize(1327, 698);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/fit2.png"), QSize(), QIcon::Normal, QIcon::Off);
        CalibrationChart->setWindowIcon(icon);
        CalibrationChart->setStyleSheet(QString::fromUtf8("*{\n"
"	font: 75 12pt \"Agency FB\";\n"
"}\n"
"\n"
"QLabel\n"
"{ \n"
"	/*font: 25 10pt \"Microsoft YaHei\";  */\n"
"	border-radius: 10px;	\n"
"}\n"
"\n"
"QLineEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"QTextEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"/*\346\214\211\351\222\256\351\235\231\346\255\242\346\227\240\346\223\215\344\275\234\346\240\267\345\274\217*/\n"
"QPushButton \n"
"{\n"
"    /*background-color:blue;*/\n"
"    /*background-color: rgba(0, 102, 116, 240); \n"
"    color:white; */\n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt; */\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
" \n"
"/*\351\274\240\346\240\207\346\202\254\345\201\234\345\234\250\346\214\211\351\222\256*/\n"
"QPushButton:hover\n"
"{\n"
"    background-color: gray; \n"
"    color:rgb(6,168,255);\n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351"
                        "\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QPushButton:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"\n"
"/* === QGroupBox === */\n"
"QGroupBox {\n"
"    border: 2px solid gray;\n"
"    margin-top: 2ex;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    color: rgba(0, 102, 116, 240);\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    margin-left: 5px;\n"
"}\n"
"\n"
"\n"
"/* === QRadioButton === */\n"
"QRadioButton {\n"
"	/*background-color: rgba(0, 102, 116, 240); */\n"
"	/*color: white; */\n"
"    border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
"/* === QComboBox === */\n"
"QComboBox \n"
"{\n"
"	color:rgb(0,0,0);\n"
"	/*background:rgba(0, 102, 116, 240);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:hover\n"
"{\n"
"    color:gray;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
""
                        "QComboBox:pressed\n"
"{\n"
"    /*color:rgb(6,168,255);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:on\n"
"{\n"
"    /*color:rgb(6,168,255)*/\n"
"	/*color:rgb(255,255,255);*/;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item:selected\n"
"{	\n"
"	color:rgb(255,255,255);\n"
"	background-color:gray;\n"
"}\n"
"\n"
"\n"
"/********QGroupBox*******/\n"
"\n"
"QGroupBox \n"
"{\n"
"	color:rgb(6,168,255);\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"/********QToolBox*******/\n"
"QToolBox::tab {\n"
"    color: white;\n"
"	background-color: rgba(0, 102, 116, 240);\n"
"	font: 25 18pt \"Agency FB\";\n"
"    border: 2px solid gray;\n"
"    border-radius"
                        ":10px;\n"
"}\n"
"QToolBoxButton \n"
"{\n"
"    min-height: 40px; \n"
"	text-align: right;\n"
"}\n"
"\n"
"QToolBox::tab:hover\n"
"{\n"
"    color: gray;\n"
"	background-color: rgb(0, 170, 127);\n"
"	font: 25 18pt \"Agency FB\"; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QToolBox::tab:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"/*********/\n"
" \n"
"\n"
""));
        centralwidget = new QWidget(CalibrationChart);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        gridLayout_2 = new QGridLayout(centralwidget);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setSpacing(0);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        widgetCalibration = new QCustomPlot(centralwidget);
        widgetCalibration->setObjectName(QString::fromUtf8("widgetCalibration"));

        verticalLayout_3->addWidget(widgetCalibration);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setSpacing(0);
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        textEdit_TOF_PEAK = new QTextEdit(centralwidget);
        textEdit_TOF_PEAK->setObjectName(QString::fromUtf8("textEdit_TOF_PEAK"));
        textEdit_TOF_PEAK->setStyleSheet(QString::fromUtf8("font: 25 10pt \"Microsoft YaHei\";"));

        horizontalLayout_3->addWidget(textEdit_TOF_PEAK);

        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setSpacing(0);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        pushButton_SAVE_LOG = new QPushButton(centralwidget);
        pushButton_SAVE_LOG->setObjectName(QString::fromUtf8("pushButton_SAVE_LOG"));
        QSizePolicy sizePolicy(QSizePolicy::Minimum, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(pushButton_SAVE_LOG->sizePolicy().hasHeightForWidth());
        pushButton_SAVE_LOG->setSizePolicy(sizePolicy);

        verticalLayout_2->addWidget(pushButton_SAVE_LOG);

        pushButton_CLEAR_LOG = new QPushButton(centralwidget);
        pushButton_CLEAR_LOG->setObjectName(QString::fromUtf8("pushButton_CLEAR_LOG"));
        sizePolicy.setHeightForWidth(pushButton_CLEAR_LOG->sizePolicy().hasHeightForWidth());
        pushButton_CLEAR_LOG->setSizePolicy(sizePolicy);

        verticalLayout_2->addWidget(pushButton_CLEAR_LOG);

        lineEdit_FILE_NAME = new QLineEdit(centralwidget);
        lineEdit_FILE_NAME->setObjectName(QString::fromUtf8("lineEdit_FILE_NAME"));
        QSizePolicy sizePolicy1(QSizePolicy::Expanding, QSizePolicy::Preferred);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(lineEdit_FILE_NAME->sizePolicy().hasHeightForWidth());
        lineEdit_FILE_NAME->setSizePolicy(sizePolicy1);

        verticalLayout_2->addWidget(lineEdit_FILE_NAME);


        horizontalLayout_3->addLayout(verticalLayout_2);

        horizontalLayout_3->setStretch(0, 4);
        horizontalLayout_3->setStretch(1, 1);

        verticalLayout_3->addLayout(horizontalLayout_3);

        verticalLayout_3->setStretch(0, 5);
        verticalLayout_3->setStretch(1, 3);

        horizontalLayout_4->addLayout(verticalLayout_3);

        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        stopCMD = new QPushButton(centralwidget);
        stopCMD->setObjectName(QString::fromUtf8("stopCMD"));
        QSizePolicy sizePolicy2(QSizePolicy::Preferred, QSizePolicy::Preferred);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(stopCMD->sizePolicy().hasHeightForWidth());
        stopCMD->setSizePolicy(sizePolicy2);
        QFont font;
        font.setFamily(QString::fromUtf8("Agency FB"));
        font.setPointSize(20);
        font.setBold(false);
        font.setItalic(false);
        font.setWeight(50);
        stopCMD->setFont(font);
        stopCMD->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 12, 44);\n"
"font: 20pt \"Agency FB\";"));

        horizontalLayout->addWidget(stopCMD);

        resetCMD = new QPushButton(centralwidget);
        resetCMD->setObjectName(QString::fromUtf8("resetCMD"));
        sizePolicy2.setHeightForWidth(resetCMD->sizePolicy().hasHeightForWidth());
        resetCMD->setSizePolicy(sizePolicy2);
        resetCMD->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 85, 0);\n"
"font: 20pt \"Agency FB\";"));

        horizontalLayout->addWidget(resetCMD);


        verticalLayout->addLayout(horizontalLayout);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        label = new QLabel(centralwidget);
        label->setObjectName(QString::fromUtf8("label"));

        horizontalLayout_2->addWidget(label);

        offset = new QLineEdit(centralwidget);
        offset->setObjectName(QString::fromUtf8("offset"));
        offset->setAlignment(Qt::AlignCenter);

        horizontalLayout_2->addWidget(offset);


        verticalLayout->addLayout(horizontalLayout_2);


        verticalLayout_4->addLayout(verticalLayout);

        groupBox = new QGroupBox(centralwidget);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        layoutWidget = new QWidget(groupBox);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(50, 30, 281, 151));
        horizontalLayout_5 = new QHBoxLayout(layoutWidget);
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        horizontalLayout_5->setContentsMargins(0, 0, 0, 0);
        verticalLayout_5 = new QVBoxLayout();
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        label_2 = new QLabel(layoutWidget);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setAlignment(Qt::AlignCenter);

        verticalLayout_5->addWidget(label_2);

        settingDistance = new QLineEdit(layoutWidget);
        settingDistance->setObjectName(QString::fromUtf8("settingDistance"));
        sizePolicy2.setHeightForWidth(settingDistance->sizePolicy().hasHeightForWidth());
        settingDistance->setSizePolicy(sizePolicy2);
        settingDistance->setAlignment(Qt::AlignCenter);

        verticalLayout_5->addWidget(settingDistance);

        label_3 = new QLabel(layoutWidget);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setAlignment(Qt::AlignCenter);

        verticalLayout_5->addWidget(label_3);

        settingSpeed = new QLineEdit(layoutWidget);
        settingSpeed->setObjectName(QString::fromUtf8("settingSpeed"));
        sizePolicy2.setHeightForWidth(settingSpeed->sizePolicy().hasHeightForWidth());
        settingSpeed->setSizePolicy(sizePolicy2);
        settingSpeed->setAlignment(Qt::AlignCenter);

        verticalLayout_5->addWidget(settingSpeed);


        horizontalLayout_5->addLayout(verticalLayout_5);

        RunDis = new QPushButton(layoutWidget);
        RunDis->setObjectName(QString::fromUtf8("RunDis"));
        sizePolicy2.setHeightForWidth(RunDis->sizePolicy().hasHeightForWidth());
        RunDis->setSizePolicy(sizePolicy2);
        RunDis->setStyleSheet(QString::fromUtf8("font: 20pt \"Agency FB\";"));

        horizontalLayout_5->addWidget(RunDis);


        verticalLayout_4->addWidget(groupBox);

        groupBox_2 = new QGroupBox(centralwidget);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        tableView = new QTableView(groupBox_2);
        tableView->setObjectName(QString::fromUtf8("tableView"));
        tableView->setGeometry(QRect(20, 80, 321, 151));
        exeRun = new QPushButton(groupBox_2);
        exeRun->setObjectName(QString::fromUtf8("exeRun"));
        exeRun->setGeometry(QRect(218, 243, 121, 71));
        layoutWidget1 = new QWidget(groupBox_2);
        layoutWidget1->setObjectName(QString::fromUtf8("layoutWidget1"));
        layoutWidget1->setGeometry(QRect(20, 240, 191, 71));
        gridLayout = new QGridLayout(layoutWidget1);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setContentsMargins(0, 0, 0, 0);
        label_4 = new QLabel(layoutWidget1);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setAlignment(Qt::AlignCenter);

        gridLayout->addWidget(label_4, 0, 0, 1, 1);

        label_5 = new QLabel(layoutWidget1);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setAlignment(Qt::AlignCenter);

        gridLayout->addWidget(label_5, 0, 1, 1, 1);

        exeNum = new QLineEdit(layoutWidget1);
        exeNum->setObjectName(QString::fromUtf8("exeNum"));
        sizePolicy2.setHeightForWidth(exeNum->sizePolicy().hasHeightForWidth());
        exeNum->setSizePolicy(sizePolicy2);
        exeNum->setAlignment(Qt::AlignCenter);

        gridLayout->addWidget(exeNum, 1, 0, 1, 1);

        saveFileName = new QLineEdit(layoutWidget1);
        saveFileName->setObjectName(QString::fromUtf8("saveFileName"));
        sizePolicy2.setHeightForWidth(saveFileName->sizePolicy().hasHeightForWidth());
        saveFileName->setSizePolicy(sizePolicy2);
        saveFileName->setAlignment(Qt::AlignCenter);

        gridLayout->addWidget(saveFileName, 1, 1, 1, 1);

        layoutWidget2 = new QWidget(groupBox_2);
        layoutWidget2->setObjectName(QString::fromUtf8("layoutWidget2"));
        layoutWidget2->setGeometry(QRect(20, 30, 321, 32));
        horizontalLayout_6 = new QHBoxLayout(layoutWidget2);
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        horizontalLayout_6->setContentsMargins(0, 0, 0, 0);
        label_6 = new QLabel(layoutWidget2);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setAlignment(Qt::AlignCenter);

        horizontalLayout_6->addWidget(label_6);

        samplePointDis = new QLineEdit(layoutWidget2);
        samplePointDis->setObjectName(QString::fromUtf8("samplePointDis"));
        sizePolicy2.setHeightForWidth(samplePointDis->sizePolicy().hasHeightForWidth());
        samplePointDis->setSizePolicy(sizePolicy2);
        samplePointDis->setAlignment(Qt::AlignCenter);

        horizontalLayout_6->addWidget(samplePointDis);

        addSamplePoint = new QPushButton(layoutWidget2);
        addSamplePoint->setObjectName(QString::fromUtf8("addSamplePoint"));
        sizePolicy2.setHeightForWidth(addSamplePoint->sizePolicy().hasHeightForWidth());
        addSamplePoint->setSizePolicy(sizePolicy2);

        horizontalLayout_6->addWidget(addSamplePoint);


        verticalLayout_4->addWidget(groupBox_2);

        verticalLayout_4->setStretch(0, 2);
        verticalLayout_4->setStretch(1, 3);
        verticalLayout_4->setStretch(2, 5);

        horizontalLayout_4->addLayout(verticalLayout_4);

        horizontalLayout_4->setStretch(0, 5);
        horizontalLayout_4->setStretch(1, 2);

        gridLayout_2->addLayout(horizontalLayout_4, 0, 0, 1, 1);

        CalibrationChart->setCentralWidget(centralwidget);

        retranslateUi(CalibrationChart);

        QMetaObject::connectSlotsByName(CalibrationChart);
    } // setupUi

    void retranslateUi(QMainWindow *CalibrationChart)
    {
        CalibrationChart->setWindowTitle(QCoreApplication::translate("CalibrationChart", "calibration", nullptr));
        pushButton_SAVE_LOG->setText(QCoreApplication::translate("CalibrationChart", "saveLog", nullptr));
        pushButton_CLEAR_LOG->setText(QCoreApplication::translate("CalibrationChart", "StopLog", nullptr));
        stopCMD->setText(QCoreApplication::translate("CalibrationChart", "STOP", nullptr));
        resetCMD->setText(QCoreApplication::translate("CalibrationChart", "RESET", nullptr));
        label->setText(QCoreApplication::translate("CalibrationChart", "OFFSET:(mm)", nullptr));
        offset->setText(QCoreApplication::translate("CalibrationChart", "50", nullptr));
        groupBox->setTitle(QCoreApplication::translate("CalibrationChart", "setp over:", nullptr));
        label_2->setText(QCoreApplication::translate("CalibrationChart", "setting Dis(mm)", nullptr));
        label_3->setText(QCoreApplication::translate("CalibrationChart", "setting Spd(mm/s)", nullptr));
        RunDis->setText(QCoreApplication::translate("CalibrationChart", "RUN", nullptr));
        groupBox_2->setTitle(QCoreApplication::translate("CalibrationChart", "auto run", nullptr));
        exeRun->setText(QCoreApplication::translate("CalibrationChart", "RUN", nullptr));
        label_4->setText(QCoreApplication::translate("CalibrationChart", "exeTimes:", nullptr));
        label_5->setText(QCoreApplication::translate("CalibrationChart", "fileName", nullptr));
        label_6->setText(QCoreApplication::translate("CalibrationChart", "add SamplePoint", nullptr));
        addSamplePoint->setText(QCoreApplication::translate("CalibrationChart", "add", nullptr));
    } // retranslateUi

};

namespace Ui {
    class CalibrationChart: public Ui_CalibrationChart {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CALIBRATIONCHART_H
