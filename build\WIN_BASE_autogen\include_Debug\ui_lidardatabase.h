/********************************************************************************
** Form generated from reading UI file 'lidardatabase.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_LIDARDATABASE_H
#define UI_LIDARDATABASE_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTableView>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_LidarDatabase
{
public:
    QAction *actionLCopy;
    QAction *actionDShowIamge;
    QAction *actionDSchOnce;
    QAction *actionDSchAll;
    QAction *actionDSubmit;
    QAction *actionDSubmitAll;
    QAction *actionDInsert;
    QAction *actionDIstImg;
    QAction *actionDRevert;
    QAction *actionDRevertAll;
    QAction *actionOTA;
    QWidget *centralwidget;
    QTextEdit *textEditLog;
    QTableView *tableViewLidarInfomation;
    QTableView *tableViewLidarDatabase;
    QWidget *layoutWidget;
    QHBoxLayout *horizontalLayout;
    QGroupBox *groupBox_2;
    QRadioButton *radioButtonReserve3;
    QRadioButton *radioButtonCalibration;
    QRadioButton *radioButtonDebug1;
    QRadioButton *radioButtonDebug2;
    QRadioButton *radioButtonReflectivity;
    QRadioButton *radioButtonTriMode;
    QRadioButton *radioButtonZeroAngle;
    QRadioButton *radioButtonControlFreq;
    QRadioButton *radioButtonDebug3;
    QRadioButton *radioButtonTG;
    QRadioButton *radioButtonRegister;
    QRadioButton *radioButtonLed;
    QRadioButton *radioButtonMcuID;
    QRadioButton *radioButtonVersionBaud;
    QRadioButton *radioButtonTofMode;
    QRadioButton *radioButtonTemperature;
    QRadioButton *radioButtonDebug4;
    QRadioButton *radioButtonMeasureRange;
    QRadioButton *radioButtonLaser;
    QRadioButton *radioButtonReserve4;
    QRadioButton *radioButtonReserve4_2;
    QRadioButton *radioButtonReserve4_3;
    QRadioButton *radioButtonReserve4_4;
    QRadioButton *radioButtonReserve4_5;
    QRadioButton *radioButtonReserve4_6;
    QRadioButton *radioButtonReserve4_7;
    QRadioButton *radioButtonReserve4_8;
    QRadioButton *radioButtonReserve4_9;
    QRadioButton *radioButtonReserve4_10;
    QRadioButton *radioButtonReserve4_11;
    QRadioButton *radioButtonReserve4_12;
    QRadioButton *radioButtonReserve4_13;
    QRadioButton *radioButtonReserve4_14;
    QRadioButton *radioButtonReserve4_15;
    QRadioButton *radioButtonReserve4_16;
    QRadioButton *radioButtonReserve4_17;
    QRadioButton *radioButtonReserve4_18;
    QRadioButton *radioButtonReserve4_19;
    QRadioButton *radioButtonReserve4_20;
    QRadioButton *radioButtonReserve4_21;
    QVBoxLayout *verticalLayout;
    QGroupBox *groupBox;
    QWidget *widget;
    QVBoxLayout *verticalLayout_8;
    QRadioButton *radioButtonWrite;
    QRadioButton *radioButtonRead;
    QPushButton *pushButtonTransmit;
    QLabel *label_2;
    QLabel *label_3;
    QLabel *label_4;
    QWidget *layoutWidget1;
    QVBoxLayout *verticalLayout_2;
    QLabel *label;
    QComboBox *comboBoxRunningMode;
    QProgressBar *progressBar_iap;
    QStatusBar *statusbar;
    QToolBar *toolBar;

    void setupUi(QMainWindow *LidarDatabase)
    {
        if (LidarDatabase->objectName().isEmpty())
            LidarDatabase->setObjectName(QString::fromUtf8("LidarDatabase"));
        LidarDatabase->resize(1116, 813);
        LidarDatabase->setMinimumSize(QSize(1116, 787));
        LidarDatabase->setMaximumSize(QSize(1116, 16777215));
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/database.png"), QSize(), QIcon::Normal, QIcon::Off);
        LidarDatabase->setWindowIcon(icon);
        LidarDatabase->setStyleSheet(QString::fromUtf8("*{\n"
"	font: 75 9pt \"Calisto MT\";\n"
"	/*font: 75 10pt \"Agency FB\";*/\n"
"}\n"
"\n"
"QLabel\n"
"{ \n"
"	/*font: 25 10pt \"Microsoft YaHei\";  */\n"
"	border-radius: 10px;	\n"
"}\n"
"\n"
"QLineEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"QTextEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"/*\346\214\211\351\222\256\351\235\231\346\255\242\346\227\240\346\223\215\344\275\234\346\240\267\345\274\217*/\n"
"QPushButton \n"
"{\n"
"    /*background-color:blue;*/\n"
"    /*background-color: rgba(0, 102, 116, 240); \n"
"    color:white; */\n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt; */\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
" \n"
"/*\351\274\240\346\240\207\346\202\254\345\201\234\345\234\250\346\214\211\351\222\256*/\n"
"QPushButton:hover\n"
"{\n"
"    background-color: gray; \n"
"    color:rgb(6,168,255);\n"
"    border:2px solid gray;\n"
"    bor"
                        "der-radius:10px;\n"
"}\n"
" \n"
"/*\351\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QPushButton:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"\n"
"/* === QGroupBox === */\n"
"QGroupBox {\n"
"    border: 2px solid gray;\n"
"    margin-top: 2ex;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    color: rgba(0, 102, 116, 240);\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    margin-left: 5px;\n"
"}\n"
"\n"
"\n"
"/* === QRadioButton === */\n"
"QRadioButton {\n"
"	/*background-color: rgba(0, 102, 116, 240); */\n"
"	/*color: white; */\n"
"    /*border: 2px solid gray;*/\n"
"	border-radius:10px;\n"
"}\n"
"/* === QComboBox === */\n"
"QComboBox \n"
"{\n"
"	color:rgb(0,0,0);\n"
"	/*background:rgba(0, 102, 116, 240);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:hover\n"
"{\n"
"    color:gray;\n"
"    border: 2px solid gra"
                        "y;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:pressed\n"
"{\n"
"    /*color:rgb(6,168,255);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:on\n"
"{\n"
"    /*color:rgb(6,168,255)*/\n"
"	/*color:rgb(255,255,255);*/;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item:selected\n"
"{	\n"
"	color:rgb(255,255,255);\n"
"	background-color:gray;\n"
"}\n"
"\n"
"\n"
"/********QGroupBox*******/\n"
"\n"
"QGroupBox \n"
"{\n"
"	color:rgb(6,168,255);\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"/********QToolBox*******/\n"
"QToolBox::tab {\n"
"    color: white;\n"
"	background-color: rgba(0, 102, 116, 240);\n"
"	font: 25 18pt \"Agency FB\";\n"
"   "
                        " border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"QToolBoxButton \n"
"{\n"
"    min-height: 40px; \n"
"	text-align: right;\n"
"}\n"
"\n"
"QToolBox::tab:hover\n"
"{\n"
"    color: gray;\n"
"	background-color: rgb(0, 170, 127);\n"
"	font: 25 18pt \"Agency FB\"; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QToolBox::tab:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"/*********/\n"
" \n"
"\n"
""));
        actionLCopy = new QAction(LidarDatabase);
        actionLCopy->setObjectName(QString::fromUtf8("actionLCopy"));
        actionLCopy->setCheckable(false);
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/copy.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionLCopy->setIcon(icon1);
        actionDShowIamge = new QAction(LidarDatabase);
        actionDShowIamge->setObjectName(QString::fromUtf8("actionDShowIamge"));
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/magnifier.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionDShowIamge->setIcon(icon2);
        actionDSchOnce = new QAction(LidarDatabase);
        actionDSchOnce->setObjectName(QString::fromUtf8("actionDSchOnce"));
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/searchOnce.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionDSchOnce->setIcon(icon3);
        actionDSchAll = new QAction(LidarDatabase);
        actionDSchAll->setObjectName(QString::fromUtf8("actionDSchAll"));
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/serachAll.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionDSchAll->setIcon(icon4);
        actionDSubmit = new QAction(LidarDatabase);
        actionDSubmit->setObjectName(QString::fromUtf8("actionDSubmit"));
        QIcon icon5;
        icon5.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/upLoad1.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionDSubmit->setIcon(icon5);
        actionDSubmitAll = new QAction(LidarDatabase);
        actionDSubmitAll->setObjectName(QString::fromUtf8("actionDSubmitAll"));
        QIcon icon6;
        icon6.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/upLoad.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionDSubmitAll->setIcon(icon6);
        actionDInsert = new QAction(LidarDatabase);
        actionDInsert->setObjectName(QString::fromUtf8("actionDInsert"));
        QIcon icon7;
        icon7.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/insert.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionDInsert->setIcon(icon7);
        actionDIstImg = new QAction(LidarDatabase);
        actionDIstImg->setObjectName(QString::fromUtf8("actionDIstImg"));
        QIcon icon8;
        icon8.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/insertImage.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionDIstImg->setIcon(icon8);
        actionDRevert = new QAction(LidarDatabase);
        actionDRevert->setObjectName(QString::fromUtf8("actionDRevert"));
        QIcon icon9;
        icon9.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/reveret1.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionDRevert->setIcon(icon9);
        actionDRevertAll = new QAction(LidarDatabase);
        actionDRevertAll->setObjectName(QString::fromUtf8("actionDRevertAll"));
        QIcon icon10;
        icon10.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/revertAll.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionDRevertAll->setIcon(icon10);
        actionOTA = new QAction(LidarDatabase);
        actionOTA->setObjectName(QString::fromUtf8("actionOTA"));
        QIcon icon11;
        icon11.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/download2.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionOTA->setIcon(icon11);
        centralwidget = new QWidget(LidarDatabase);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        textEditLog = new QTextEdit(centralwidget);
        textEditLog->setObjectName(QString::fromUtf8("textEditLog"));
        textEditLog->setGeometry(QRect(140, 310, 971, 211));
        tableViewLidarInfomation = new QTableView(centralwidget);
        tableViewLidarInfomation->setObjectName(QString::fromUtf8("tableViewLidarInfomation"));
        tableViewLidarInfomation->setGeometry(QRect(140, 540, 971, 151));
        tableViewLidarDatabase = new QTableView(centralwidget);
        tableViewLidarDatabase->setObjectName(QString::fromUtf8("tableViewLidarDatabase"));
        tableViewLidarDatabase->setGeometry(QRect(140, 710, 971, 181));
        layoutWidget = new QWidget(centralwidget);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(140, 10, 971, 281));
        horizontalLayout = new QHBoxLayout(layoutWidget);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        groupBox_2 = new QGroupBox(layoutWidget);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        radioButtonReserve3 = new QRadioButton(groupBox_2);
        radioButtonReserve3->setObjectName(QString::fromUtf8("radioButtonReserve3"));
        radioButtonReserve3->setGeometry(QRect(620, 80, 115, 19));
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(radioButtonReserve3->sizePolicy().hasHeightForWidth());
        radioButtonReserve3->setSizePolicy(sizePolicy);
        radioButtonCalibration = new QRadioButton(groupBox_2);
        radioButtonCalibration->setObjectName(QString::fromUtf8("radioButtonCalibration"));
        radioButtonCalibration->setGeometry(QRect(10, 50, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonCalibration->sizePolicy().hasHeightForWidth());
        radioButtonCalibration->setSizePolicy(sizePolicy);
        radioButtonDebug1 = new QRadioButton(groupBox_2);
        radioButtonDebug1->setObjectName(QString::fromUtf8("radioButtonDebug1"));
        radioButtonDebug1->setGeometry(QRect(460, 20, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonDebug1->sizePolicy().hasHeightForWidth());
        radioButtonDebug1->setSizePolicy(sizePolicy);
        radioButtonDebug2 = new QRadioButton(groupBox_2);
        radioButtonDebug2->setObjectName(QString::fromUtf8("radioButtonDebug2"));
        radioButtonDebug2->setGeometry(QRect(460, 50, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonDebug2->sizePolicy().hasHeightForWidth());
        radioButtonDebug2->setSizePolicy(sizePolicy);
        radioButtonReflectivity = new QRadioButton(groupBox_2);
        radioButtonReflectivity->setObjectName(QString::fromUtf8("radioButtonReflectivity"));
        radioButtonReflectivity->setGeometry(QRect(310, 110, 121, 19));
        sizePolicy.setHeightForWidth(radioButtonReflectivity->sizePolicy().hasHeightForWidth());
        radioButtonReflectivity->setSizePolicy(sizePolicy);
        radioButtonTriMode = new QRadioButton(groupBox_2);
        radioButtonTriMode->setObjectName(QString::fromUtf8("radioButtonTriMode"));
        radioButtonTriMode->setGeometry(QRect(150, 20, 121, 19));
        sizePolicy.setHeightForWidth(radioButtonTriMode->sizePolicy().hasHeightForWidth());
        radioButtonTriMode->setSizePolicy(sizePolicy);
        radioButtonZeroAngle = new QRadioButton(groupBox_2);
        radioButtonZeroAngle->setObjectName(QString::fromUtf8("radioButtonZeroAngle"));
        radioButtonZeroAngle->setGeometry(QRect(10, 80, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonZeroAngle->sizePolicy().hasHeightForWidth());
        radioButtonZeroAngle->setSizePolicy(sizePolicy);
        radioButtonControlFreq = new QRadioButton(groupBox_2);
        radioButtonControlFreq->setObjectName(QString::fromUtf8("radioButtonControlFreq"));
        radioButtonControlFreq->setGeometry(QRect(150, 80, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonControlFreq->sizePolicy().hasHeightForWidth());
        radioButtonControlFreq->setSizePolicy(sizePolicy);
        radioButtonDebug3 = new QRadioButton(groupBox_2);
        radioButtonDebug3->setObjectName(QString::fromUtf8("radioButtonDebug3"));
        radioButtonDebug3->setGeometry(QRect(460, 80, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonDebug3->sizePolicy().hasHeightForWidth());
        radioButtonDebug3->setSizePolicy(sizePolicy);
        radioButtonTG = new QRadioButton(groupBox_2);
        radioButtonTG->setObjectName(QString::fromUtf8("radioButtonTG"));
        radioButtonTG->setGeometry(QRect(310, 80, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonTG->sizePolicy().hasHeightForWidth());
        radioButtonTG->setSizePolicy(sizePolicy);
        radioButtonRegister = new QRadioButton(groupBox_2);
        radioButtonRegister->setObjectName(QString::fromUtf8("radioButtonRegister"));
        radioButtonRegister->setGeometry(QRect(10, 110, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonRegister->sizePolicy().hasHeightForWidth());
        radioButtonRegister->setSizePolicy(sizePolicy);
        radioButtonLed = new QRadioButton(groupBox_2);
        radioButtonLed->setObjectName(QString::fromUtf8("radioButtonLed"));
        radioButtonLed->setGeometry(QRect(620, 50, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonLed->sizePolicy().hasHeightForWidth());
        radioButtonLed->setSizePolicy(sizePolicy);
        radioButtonMcuID = new QRadioButton(groupBox_2);
        radioButtonMcuID->setObjectName(QString::fromUtf8("radioButtonMcuID"));
        radioButtonMcuID->setGeometry(QRect(150, 50, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonMcuID->sizePolicy().hasHeightForWidth());
        radioButtonMcuID->setSizePolicy(sizePolicy);
        radioButtonVersionBaud = new QRadioButton(groupBox_2);
        radioButtonVersionBaud->setObjectName(QString::fromUtf8("radioButtonVersionBaud"));
        radioButtonVersionBaud->setGeometry(QRect(310, 50, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonVersionBaud->sizePolicy().hasHeightForWidth());
        radioButtonVersionBaud->setSizePolicy(sizePolicy);
        radioButtonTofMode = new QRadioButton(groupBox_2);
        radioButtonTofMode->setObjectName(QString::fromUtf8("radioButtonTofMode"));
        radioButtonTofMode->setGeometry(QRect(10, 20, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonTofMode->sizePolicy().hasHeightForWidth());
        radioButtonTofMode->setSizePolicy(sizePolicy);
        radioButtonTofMode->setChecked(true);
        radioButtonTemperature = new QRadioButton(groupBox_2);
        radioButtonTemperature->setObjectName(QString::fromUtf8("radioButtonTemperature"));
        radioButtonTemperature->setGeometry(QRect(150, 110, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonTemperature->sizePolicy().hasHeightForWidth());
        radioButtonTemperature->setSizePolicy(sizePolicy);
        radioButtonDebug4 = new QRadioButton(groupBox_2);
        radioButtonDebug4->setObjectName(QString::fromUtf8("radioButtonDebug4"));
        radioButtonDebug4->setGeometry(QRect(460, 110, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonDebug4->sizePolicy().hasHeightForWidth());
        radioButtonDebug4->setSizePolicy(sizePolicy);
        radioButtonMeasureRange = new QRadioButton(groupBox_2);
        radioButtonMeasureRange->setObjectName(QString::fromUtf8("radioButtonMeasureRange"));
        radioButtonMeasureRange->setGeometry(QRect(310, 20, 131, 19));
        sizePolicy.setHeightForWidth(radioButtonMeasureRange->sizePolicy().hasHeightForWidth());
        radioButtonMeasureRange->setSizePolicy(sizePolicy);
        radioButtonLaser = new QRadioButton(groupBox_2);
        radioButtonLaser->setObjectName(QString::fromUtf8("radioButtonLaser"));
        radioButtonLaser->setGeometry(QRect(620, 20, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonLaser->sizePolicy().hasHeightForWidth());
        radioButtonLaser->setSizePolicy(sizePolicy);
        radioButtonReserve4 = new QRadioButton(groupBox_2);
        radioButtonReserve4->setObjectName(QString::fromUtf8("radioButtonReserve4"));
        radioButtonReserve4->setGeometry(QRect(620, 110, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4->sizePolicy().hasHeightForWidth());
        radioButtonReserve4->setSizePolicy(sizePolicy);
        radioButtonReserve4_2 = new QRadioButton(groupBox_2);
        radioButtonReserve4_2->setObjectName(QString::fromUtf8("radioButtonReserve4_2"));
        radioButtonReserve4_2->setGeometry(QRect(10, 150, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_2->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_2->setSizePolicy(sizePolicy);
        radioButtonReserve4_3 = new QRadioButton(groupBox_2);
        radioButtonReserve4_3->setObjectName(QString::fromUtf8("radioButtonReserve4_3"));
        radioButtonReserve4_3->setGeometry(QRect(10, 180, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_3->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_3->setSizePolicy(sizePolicy);
        radioButtonReserve4_4 = new QRadioButton(groupBox_2);
        radioButtonReserve4_4->setObjectName(QString::fromUtf8("radioButtonReserve4_4"));
        radioButtonReserve4_4->setGeometry(QRect(10, 210, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_4->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_4->setSizePolicy(sizePolicy);
        radioButtonReserve4_5 = new QRadioButton(groupBox_2);
        radioButtonReserve4_5->setObjectName(QString::fromUtf8("radioButtonReserve4_5"));
        radioButtonReserve4_5->setGeometry(QRect(10, 240, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_5->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_5->setSizePolicy(sizePolicy);
        radioButtonReserve4_6 = new QRadioButton(groupBox_2);
        radioButtonReserve4_6->setObjectName(QString::fromUtf8("radioButtonReserve4_6"));
        radioButtonReserve4_6->setGeometry(QRect(150, 180, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_6->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_6->setSizePolicy(sizePolicy);
        radioButtonReserve4_7 = new QRadioButton(groupBox_2);
        radioButtonReserve4_7->setObjectName(QString::fromUtf8("radioButtonReserve4_7"));
        radioButtonReserve4_7->setGeometry(QRect(150, 150, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_7->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_7->setSizePolicy(sizePolicy);
        radioButtonReserve4_8 = new QRadioButton(groupBox_2);
        radioButtonReserve4_8->setObjectName(QString::fromUtf8("radioButtonReserve4_8"));
        radioButtonReserve4_8->setGeometry(QRect(150, 210, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_8->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_8->setSizePolicy(sizePolicy);
        radioButtonReserve4_9 = new QRadioButton(groupBox_2);
        radioButtonReserve4_9->setObjectName(QString::fromUtf8("radioButtonReserve4_9"));
        radioButtonReserve4_9->setGeometry(QRect(150, 240, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_9->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_9->setSizePolicy(sizePolicy);
        radioButtonReserve4_10 = new QRadioButton(groupBox_2);
        radioButtonReserve4_10->setObjectName(QString::fromUtf8("radioButtonReserve4_10"));
        radioButtonReserve4_10->setGeometry(QRect(310, 180, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_10->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_10->setSizePolicy(sizePolicy);
        radioButtonReserve4_11 = new QRadioButton(groupBox_2);
        radioButtonReserve4_11->setObjectName(QString::fromUtf8("radioButtonReserve4_11"));
        radioButtonReserve4_11->setGeometry(QRect(310, 150, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_11->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_11->setSizePolicy(sizePolicy);
        radioButtonReserve4_12 = new QRadioButton(groupBox_2);
        radioButtonReserve4_12->setObjectName(QString::fromUtf8("radioButtonReserve4_12"));
        radioButtonReserve4_12->setGeometry(QRect(310, 210, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_12->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_12->setSizePolicy(sizePolicy);
        radioButtonReserve4_13 = new QRadioButton(groupBox_2);
        radioButtonReserve4_13->setObjectName(QString::fromUtf8("radioButtonReserve4_13"));
        radioButtonReserve4_13->setGeometry(QRect(310, 240, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_13->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_13->setSizePolicy(sizePolicy);
        radioButtonReserve4_14 = new QRadioButton(groupBox_2);
        radioButtonReserve4_14->setObjectName(QString::fromUtf8("radioButtonReserve4_14"));
        radioButtonReserve4_14->setGeometry(QRect(460, 180, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_14->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_14->setSizePolicy(sizePolicy);
        radioButtonReserve4_15 = new QRadioButton(groupBox_2);
        radioButtonReserve4_15->setObjectName(QString::fromUtf8("radioButtonReserve4_15"));
        radioButtonReserve4_15->setGeometry(QRect(460, 150, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_15->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_15->setSizePolicy(sizePolicy);
        radioButtonReserve4_16 = new QRadioButton(groupBox_2);
        radioButtonReserve4_16->setObjectName(QString::fromUtf8("radioButtonReserve4_16"));
        radioButtonReserve4_16->setGeometry(QRect(460, 210, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_16->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_16->setSizePolicy(sizePolicy);
        radioButtonReserve4_17 = new QRadioButton(groupBox_2);
        radioButtonReserve4_17->setObjectName(QString::fromUtf8("radioButtonReserve4_17"));
        radioButtonReserve4_17->setGeometry(QRect(460, 240, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_17->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_17->setSizePolicy(sizePolicy);
        radioButtonReserve4_18 = new QRadioButton(groupBox_2);
        radioButtonReserve4_18->setObjectName(QString::fromUtf8("radioButtonReserve4_18"));
        radioButtonReserve4_18->setGeometry(QRect(620, 180, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_18->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_18->setSizePolicy(sizePolicy);
        radioButtonReserve4_19 = new QRadioButton(groupBox_2);
        radioButtonReserve4_19->setObjectName(QString::fromUtf8("radioButtonReserve4_19"));
        radioButtonReserve4_19->setGeometry(QRect(620, 150, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_19->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_19->setSizePolicy(sizePolicy);
        radioButtonReserve4_20 = new QRadioButton(groupBox_2);
        radioButtonReserve4_20->setObjectName(QString::fromUtf8("radioButtonReserve4_20"));
        radioButtonReserve4_20->setGeometry(QRect(620, 210, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_20->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_20->setSizePolicy(sizePolicy);
        radioButtonReserve4_21 = new QRadioButton(groupBox_2);
        radioButtonReserve4_21->setObjectName(QString::fromUtf8("radioButtonReserve4_21"));
        radioButtonReserve4_21->setGeometry(QRect(620, 240, 115, 19));
        sizePolicy.setHeightForWidth(radioButtonReserve4_21->sizePolicy().hasHeightForWidth());
        radioButtonReserve4_21->setSizePolicy(sizePolicy);

        horizontalLayout->addWidget(groupBox_2);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        groupBox = new QGroupBox(layoutWidget);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        widget = new QWidget(groupBox);
        widget->setObjectName(QString::fromUtf8("widget"));
        widget->setGeometry(QRect(10, 20, 131, 101));
        verticalLayout_8 = new QVBoxLayout(widget);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        verticalLayout_8->setContentsMargins(0, 0, 0, 0);
        radioButtonWrite = new QRadioButton(widget);
        radioButtonWrite->setObjectName(QString::fromUtf8("radioButtonWrite"));
        sizePolicy.setHeightForWidth(radioButtonWrite->sizePolicy().hasHeightForWidth());
        radioButtonWrite->setSizePolicy(sizePolicy);
        QIcon icon12;
        icon12.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/write.png"), QSize(), QIcon::Normal, QIcon::Off);
        radioButtonWrite->setIcon(icon12);
        radioButtonWrite->setIconSize(QSize(30, 30));
        radioButtonWrite->setChecked(true);

        verticalLayout_8->addWidget(radioButtonWrite);

        radioButtonRead = new QRadioButton(widget);
        radioButtonRead->setObjectName(QString::fromUtf8("radioButtonRead"));
        sizePolicy.setHeightForWidth(radioButtonRead->sizePolicy().hasHeightForWidth());
        radioButtonRead->setSizePolicy(sizePolicy);
        radioButtonRead->setContextMenuPolicy(Qt::DefaultContextMenu);
        radioButtonRead->setAutoFillBackground(false);
        radioButtonRead->setInputMethodHints(Qt::ImhNone);
        QIcon icon13;
        icon13.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/read.png"), QSize(), QIcon::Normal, QIcon::Off);
        radioButtonRead->setIcon(icon13);
        radioButtonRead->setIconSize(QSize(30, 30));

        verticalLayout_8->addWidget(radioButtonRead);


        verticalLayout->addWidget(groupBox);

        pushButtonTransmit = new QPushButton(layoutWidget);
        pushButtonTransmit->setObjectName(QString::fromUtf8("pushButtonTransmit"));
        sizePolicy.setHeightForWidth(pushButtonTransmit->sizePolicy().hasHeightForWidth());
        pushButtonTransmit->setSizePolicy(sizePolicy);

        verticalLayout->addWidget(pushButtonTransmit);


        horizontalLayout->addLayout(verticalLayout);

        horizontalLayout->setStretch(0, 5);
        horizontalLayout->setStretch(1, 1);
        label_2 = new QLabel(centralwidget);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setGeometry(QRect(10, 450, 91, 41));
        label_3 = new QLabel(centralwidget);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setGeometry(QRect(0, 570, 111, 61));
        label_4 = new QLabel(centralwidget);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setGeometry(QRect(0, 760, 111, 41));
        layoutWidget1 = new QWidget(centralwidget);
        layoutWidget1->setObjectName(QString::fromUtf8("layoutWidget1"));
        layoutWidget1->setGeometry(QRect(0, 20, 133, 121));
        verticalLayout_2 = new QVBoxLayout(layoutWidget1);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(0, 0, 0, 0);
        label = new QLabel(layoutWidget1);
        label->setObjectName(QString::fromUtf8("label"));

        verticalLayout_2->addWidget(label);

        comboBoxRunningMode = new QComboBox(layoutWidget1);
        comboBoxRunningMode->setObjectName(QString::fromUtf8("comboBoxRunningMode"));
        sizePolicy.setHeightForWidth(comboBoxRunningMode->sizePolicy().hasHeightForWidth());
        comboBoxRunningMode->setSizePolicy(sizePolicy);
        comboBoxRunningMode->setEditable(true);

        verticalLayout_2->addWidget(comboBoxRunningMode);

        progressBar_iap = new QProgressBar(centralwidget);
        progressBar_iap->setObjectName(QString::fromUtf8("progressBar_iap"));
        progressBar_iap->setGeometry(QRect(0, 350, 131, 36));
        sizePolicy.setHeightForWidth(progressBar_iap->sizePolicy().hasHeightForWidth());
        progressBar_iap->setSizePolicy(sizePolicy);
        progressBar_iap->setStyleSheet(QString::fromUtf8("/*\346\226\221\351\251\254\347\272\277\347\232\204\350\256\276\347\275\256*/\n"
"/*QProgressBar::chunk\n"
"{\n"
"text-align:center;\n"
"font-size:48px;\n"
"border-radius:11px;\n"
"background:qlineargradient(spread:pad,x1:0,y1:0,x2:1,y2:0,stop:0 #E07502,stop:1  #E07502);\n"
"border-radius:4px;\n"
"border:0.5px solid #26B4FF;\n"
"background-color:skyblue;\n"
"width:8px;margin:0.5px;\n"
" \n"
"}\n"
"QProgressBar#progressBar\n"
"{\n"
"height:22px;\n"
"text-align:center;\n"
"font-size:48px;\n"
"color:white;\n"
"border-radius:11px;\n"
"background: #E07502 ;\n"
"}*/\n"
"QProgressBar {\n"
"    border: 2px solid grey;\n"
"    border-radius: 5px;\n"
"    text-align: center;/*\346\226\207\345\255\227\347\232\204\344\275\215\347\275\256*/\n"
"}\n"
"\n"
"QProgressBar::chunk {\n"
"    background-color: #05B8CC;\n"
"    width: 20px;/*\350\277\233\345\272\246\346\235\241\346\257\217\351\232\224\347\232\204\345\256\275\345\272\246*/\n"
"}\n"
"\n"
"QProgressBar::chunk {\n"
"    background-color: #E07502;\n"
"    width: 10px;\n"
""
                        "    margin: 0.5px;/*\345\210\273\345\272\246\344\271\213\351\227\264\347\232\204\351\227\264\351\232\224*/\n"
"}\n"
"\n"
""));
        progressBar_iap->setValue(0);
        LidarDatabase->setCentralWidget(centralwidget);
        statusbar = new QStatusBar(LidarDatabase);
        statusbar->setObjectName(QString::fromUtf8("statusbar"));
        LidarDatabase->setStatusBar(statusbar);
        toolBar = new QToolBar(LidarDatabase);
        toolBar->setObjectName(QString::fromUtf8("toolBar"));
        sizePolicy.setHeightForWidth(toolBar->sizePolicy().hasHeightForWidth());
        toolBar->setSizePolicy(sizePolicy);
        toolBar->setIconSize(QSize(50, 50));
        toolBar->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);
        LidarDatabase->addToolBar(Qt::TopToolBarArea, toolBar);

        toolBar->addAction(actionLCopy);
        toolBar->addAction(actionDShowIamge);
        toolBar->addSeparator();
        toolBar->addAction(actionDInsert);
        toolBar->addAction(actionDIstImg);
        toolBar->addSeparator();
        toolBar->addAction(actionDSubmit);
        toolBar->addAction(actionDSubmitAll);
        toolBar->addSeparator();
        toolBar->addAction(actionDRevert);
        toolBar->addAction(actionDRevertAll);
        toolBar->addSeparator();
        toolBar->addAction(actionDSchOnce);
        toolBar->addAction(actionDSchAll);
        toolBar->addSeparator();
        toolBar->addAction(actionOTA);

        retranslateUi(LidarDatabase);

        QMetaObject::connectSlotsByName(LidarDatabase);
    } // setupUi

    void retranslateUi(QMainWindow *LidarDatabase)
    {
        LidarDatabase->setWindowTitle(QCoreApplication::translate("LidarDatabase", "Database", nullptr));
        actionLCopy->setText(QCoreApplication::translate("LidarDatabase", "LCopy", nullptr));
#if QT_CONFIG(tooltip)
        actionLCopy->setToolTip(QCoreApplication::translate("LidarDatabase", "\346\213\267\350\264\235\346\225\260\346\215\256", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDShowIamge->setText(QCoreApplication::translate("LidarDatabase", "DShowImage", nullptr));
#if QT_CONFIG(tooltip)
        actionDShowIamge->setToolTip(QCoreApplication::translate("LidarDatabase", "\346\230\276\347\244\272\345\233\276\347\211\207", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDSchOnce->setText(QCoreApplication::translate("LidarDatabase", "DSchOnce", nullptr));
#if QT_CONFIG(tooltip)
        actionDSchOnce->setToolTip(QCoreApplication::translate("LidarDatabase", "\346\237\245\350\257\242", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDSchAll->setText(QCoreApplication::translate("LidarDatabase", "DSchAll", nullptr));
#if QT_CONFIG(tooltip)
        actionDSchAll->setToolTip(QCoreApplication::translate("LidarDatabase", "\346\237\245\350\257\242\346\211\200\346\234\211", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDSubmit->setText(QCoreApplication::translate("LidarDatabase", "DSubmit", nullptr));
#if QT_CONFIG(tooltip)
        actionDSubmit->setToolTip(QCoreApplication::translate("LidarDatabase", "\346\217\220\344\272\244\344\270\200\346\235\241", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDSubmitAll->setText(QCoreApplication::translate("LidarDatabase", "DSubmitAll", nullptr));
#if QT_CONFIG(tooltip)
        actionDSubmitAll->setToolTip(QCoreApplication::translate("LidarDatabase", "\346\217\220\344\272\244\346\211\200\346\234\211", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDInsert->setText(QCoreApplication::translate("LidarDatabase", "DInsert", nullptr));
#if QT_CONFIG(tooltip)
        actionDInsert->setToolTip(QCoreApplication::translate("LidarDatabase", "\346\217\222\345\205\245\344\270\200\346\235\241\346\225\260\346\215\256", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDIstImg->setText(QCoreApplication::translate("LidarDatabase", "DIstImg", nullptr));
#if QT_CONFIG(tooltip)
        actionDIstImg->setToolTip(QCoreApplication::translate("LidarDatabase", "\346\217\222\345\205\245\345\233\276\347\211\207", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDRevert->setText(QCoreApplication::translate("LidarDatabase", "DRevert", nullptr));
#if QT_CONFIG(tooltip)
        actionDRevert->setToolTip(QCoreApplication::translate("LidarDatabase", "\346\222\244\351\224\200\344\270\200\346\235\241", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDRevertAll->setText(QCoreApplication::translate("LidarDatabase", "DRevertAll", nullptr));
#if QT_CONFIG(tooltip)
        actionDRevertAll->setToolTip(QCoreApplication::translate("LidarDatabase", "\346\222\244\351\224\200\346\211\200\346\234\211\346\223\215\344\275\234", nullptr));
#endif // QT_CONFIG(tooltip)
        actionOTA->setText(QCoreApplication::translate("LidarDatabase", "OTA", nullptr));
        textEditLog->setHtml(QCoreApplication::translate("LidarDatabase", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:'Calisto MT'; font-size:9pt; font-weight:72; font-style:normal;\">\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-family:'SimSun'; font-weight:400;\"><br /></p></body></html>", nullptr));
        groupBox_2->setTitle(QString());
        radioButtonReserve3->setText(QCoreApplication::translate("LidarDatabase", "vbd", nullptr));
        radioButtonCalibration->setText(QCoreApplication::translate("LidarDatabase", "Calibration", nullptr));
        radioButtonDebug1->setText(QCoreApplication::translate("LidarDatabase", "Peak2", nullptr));
        radioButtonDebug2->setText(QCoreApplication::translate("LidarDatabase", "Unlock", nullptr));
        radioButtonReflectivity->setText(QCoreApplication::translate("LidarDatabase", "Reflectivity", nullptr));
        radioButtonTriMode->setText(QCoreApplication::translate("LidarDatabase", "Trigonometry", nullptr));
        radioButtonZeroAngle->setText(QCoreApplication::translate("LidarDatabase", "ZeroAngle", nullptr));
        radioButtonControlFreq->setText(QCoreApplication::translate("LidarDatabase", "controlFreq", nullptr));
        radioButtonDebug3->setText(QCoreApplication::translate("LidarDatabase", "Boot", nullptr));
        radioButtonTG->setText(QCoreApplication::translate("LidarDatabase", "TG", nullptr));
        radioButtonRegister->setText(QCoreApplication::translate("LidarDatabase", "Register", nullptr));
        radioButtonLed->setText(QCoreApplication::translate("LidarDatabase", "led", nullptr));
        radioButtonMcuID->setText(QCoreApplication::translate("LidarDatabase", "McuID", nullptr));
        radioButtonVersionBaud->setText(QCoreApplication::translate("LidarDatabase", "VerBaud", nullptr));
        radioButtonTofMode->setText(QCoreApplication::translate("LidarDatabase", "ToF", nullptr));
        radioButtonTemperature->setText(QCoreApplication::translate("LidarDatabase", "Temperature", nullptr));
        radioButtonDebug4->setText(QCoreApplication::translate("LidarDatabase", "ChaHist", nullptr));
        radioButtonMeasureRange->setText(QCoreApplication::translate("LidarDatabase", "MeasureRange", nullptr));
        radioButtonLaser->setText(QCoreApplication::translate("LidarDatabase", "laser", nullptr));
        radioButtonReserve4->setText(QCoreApplication::translate("LidarDatabase", "Xtalk", nullptr));
        radioButtonReserve4_2->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_3->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_4->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_5->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_6->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_7->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_8->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_9->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_10->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_11->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_12->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_13->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_14->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_15->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_16->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_17->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_18->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_19->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_20->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        radioButtonReserve4_21->setText(QCoreApplication::translate("LidarDatabase", "Reserve", nullptr));
        groupBox->setTitle(QString());
        radioButtonWrite->setText(QCoreApplication::translate("LidarDatabase", "Write", nullptr));
        radioButtonRead->setText(QCoreApplication::translate("LidarDatabase", "Read", nullptr));
        pushButtonTransmit->setText(QCoreApplication::translate("LidarDatabase", "Transmit", nullptr));
        label_2->setText(QCoreApplication::translate("LidarDatabase", "CurrentLog", nullptr));
        label_3->setText(QCoreApplication::translate("LidarDatabase", "SettingLidar", nullptr));
        label_4->setText(QCoreApplication::translate("LidarDatabase", "LidarDatabase", nullptr));
        label->setText(QCoreApplication::translate("LidarDatabase", "SettingRunMode", nullptr));
        toolBar->setWindowTitle(QCoreApplication::translate("LidarDatabase", "toolBar", nullptr));
    } // retranslateUi

};

namespace Ui {
    class LidarDatabase: public Ui_LidarDatabase {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_LIDARDATABASE_H
