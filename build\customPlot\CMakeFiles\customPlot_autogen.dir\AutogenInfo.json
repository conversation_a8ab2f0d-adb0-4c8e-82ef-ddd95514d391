{"BUILD_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen", "CMAKE_BINARY_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build", "CMAKE_CURRENT_BINARY_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot", "CMAKE_CURRENT_SOURCE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot", "CMAKE_EXECUTABLE": "D:/Programs/CMake/bin/cmake.exe", "CMAKE_LIST_FILES": ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot/CMakeLists.txt", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfigVersion.cmake", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfig.cmake"], "CMAKE_SOURCE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot/axistag.h", "MU", "EWIEGA46WW/moc_axistag.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot/mytracer.h", "MU", "EWIEGA46WW/moc_mytracer.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot/qcpdocumentobject.h", "MU", "EWIEGA46WW/moc_qcpdocumentobject.cpp", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot/qcustomplot.h", "MU", "EWIEGA46WW/moc_qcustomplot.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen/include", "INCLUDE_DIR_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen/include_Release", "MOC_COMPILATION_FILE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/customPlot_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": ["APP_VERSION=\"2.3.11\"", "QT_CORE_LIB", "QT_DEPRECATED_WARNINGS", "QT_GUI_LIB", "QT_MESSAGELOGCONTEXT", "QT_NO_DEBUG", "QT_PRINTSUPPORT_LIB", "QT_WIDGETS_LIB", "USING_CUSTOM_HIDE=0", "USING_CUSTOM_STYLE=0", "USING_LOG_TO_FILE=0", "WIN32"], "MOC_DEFINITIONS_Debug": ["APP_VERSION=\"2.3.11\"", "QT_CORE_LIB", "QT_DEPRECATED_WARNINGS", "QT_GUI_LIB", "QT_MESSAGELOGCONTEXT", "QT_PRINTSUPPORT_LIB", "QT_WIDGETS_LIB", "USING_CUSTOM_HIDE=0", "USING_CUSTOM_STYLE=0", "USING_LOG_TO_FILE=0", "WIN32"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/polynomialFit", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot", "F:/13_<PERSON><PERSON>-<PERSON><PERSON>-DTof2dMS/development/tool/winbase_app_0908/eigen-3.4.0", "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/dll", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtCore", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/mkspecs/win32-g++", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtGui", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtANGLE", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtWidgets", "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtPrintSupport"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": true, "PARALLEL": 4, "PARSE_CACHE_FILE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/CMakeFiles/customPlot_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/CMakeFiles/customPlot_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/CMakeFiles/customPlot_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/CMakeFiles/customPlot_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/CMakeFiles/customPlot_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 14, "SETTINGS_FILE": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/CMakeFiles/customPlot_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/CMakeFiles/customPlot_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/CMakeFiles/customPlot_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/CMakeFiles/customPlot_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/build/customPlot/CMakeFiles/customPlot_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot/axistag.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot/mytracer.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot/qcpdocumentobject.cpp", "MU", null], ["F:/13_<PERSON><PERSON>-<PERSON>er-DTof2dMS/development/tool/winbase_app_0908/customPlot/qcustomplot.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "VERBOSITY": 0}