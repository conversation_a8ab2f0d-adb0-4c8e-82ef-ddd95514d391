/********************************************************************************
** Form generated from reading UI file 'dynamiccalibration.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_DYNAMICCALIBRATION_H
#define UI_DYNAMICCALIBRATION_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "qcustomplot.h"

QT_BEGIN_NAMESPACE

class Ui_DynamicCalibration
{
public:
    QAction *actionTriTransmit;
    QAction *actionTriClear;
    QAction *actionTofTransmit;
    QAction *actionTofClear;
    QAction *actionImptParam;
    QAction *actionExptParam;
    QAction *actioncompensationAngle;
    QAction *actionSamplePoint;
    QAction *actionCompareData;
    QWidget *centralwidget;
    QGridLayout *gridLayout_2;
    QVBoxLayout *verticalLayout_2;
    QHBoxLayout *horizontalLayout_3;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout;
    QLabel *label;
    QComboBox *comboBoxMode;
    QTableWidget *tableWidget;
    QCustomPlot *widget;
    QHBoxLayout *horizontalLayout_2;
    QTextEdit *textEditDisLog;
    QTextEdit *textEditPeakLog;
    QGridLayout *gridLayout;
    QRadioButton *radioButton;
    QPushButton *pushButtonStaticCalibration;
    QPushButton *pushButtonDynamicCalibration;
    QToolBar *toolBar;

    void setupUi(QMainWindow *DynamicCalibration)
    {
        if (DynamicCalibration->objectName().isEmpty())
            DynamicCalibration->setObjectName(QString::fromUtf8("DynamicCalibration"));
        DynamicCalibration->resize(1011, 606);
        DynamicCalibration->setStyleSheet(QString::fromUtf8("*{\n"
"	font: 75 12pt \"Agency FB\";\n"
"}\n"
"\n"
"QLabel\n"
"{ \n"
"	/*font: 25 10pt \"Microsoft YaHei\";  */\n"
"	border-radius: 10px;	\n"
"}\n"
"\n"
"QLineEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"QTextEdit\n"
"{ \n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt;*/ \n"
"    border-radius:10px;\n"
"	\n"
"}\n"
"\n"
"/*\346\214\211\351\222\256\351\235\231\346\255\242\346\227\240\346\223\215\344\275\234\346\240\267\345\274\217*/\n"
"QPushButton \n"
"{\n"
"    /*background-color:blue;*/\n"
"    /*background-color: rgba(0, 102, 116, 240); \n"
"    color:white; */\n"
"    border:2px solid gray;\n"
"    /*font: 25 10pt; */\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
" \n"
"/*\351\274\240\346\240\207\346\202\254\345\201\234\345\234\250\346\214\211\351\222\256*/\n"
"QPushButton:hover\n"
"{\n"
"    background-color: gray; \n"
"    color:rgb(6,168,255);\n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351"
                        "\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QPushButton:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"\n"
"/* === QGroupBox === */\n"
"QGroupBox {\n"
"    border: 2px solid gray;\n"
"    margin-top: 2ex;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    color: rgba(0, 102, 116, 240);\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    margin-left: 5px;\n"
"}\n"
"\n"
"\n"
"/* === QRadioButton === */\n"
"QRadioButton {\n"
"	/*background-color: rgba(0, 102, 116, 240); */\n"
"	/*color: white; */\n"
"    border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
"/* === QComboBox === */\n"
"QComboBox \n"
"{\n"
"	color:rgb(0,0,0);\n"
"	/*background:rgba(0, 102, 116, 240);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:hover\n"
"{\n"
"    color:gray;\n"
"	/*background:rgba(0, 102, 116, 240);*/\n"
"    border: 2px solid gr"
                        "ay;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:pressed\n"
"{\n"
"    /*color:rgb(6,168,255);*/\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox:on\n"
"{\n"
"    /*color:rgb(6,168,255)*/\n"
"	/*color:rgb(255,255,255);*/;\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border: 2px solid gray;\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item\n"
"{\n"
"	/*color:rgb(6,168,255);*/\n"
"	border-radius:10px;\n"
"}\n"
" \n"
"QComboBox QAbstractItemView::item:selected\n"
"{	\n"
"	color:rgb(255,255,255);\n"
"	background-color:gray;\n"
"}\n"
"\n"
"\n"
"/********QGroupBox*******/\n"
"\n"
"QGroupBox \n"
"{\n"
"	color:rgb(6,168,255);\n"
"    border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"/********QToolBox*******/\n"
"QToolBox::tab {\n"
"    color: white;\n"
"	background-color: rgba(0, 102, 116, 240);\n"
"	font: 25 18pt \"Agency FB\";\n"
"  "
                        "  border: 2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"QToolBoxButton \n"
"{\n"
"    min-height: 40px; \n"
"	text-align: right;\n"
"}\n"
"\n"
"QToolBox::tab:hover\n"
"{\n"
"    color: gray;\n"
"	background-color: rgb(0, 170, 127);\n"
"	font: 25 18pt \"Agency FB\"; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
" \n"
"/*\351\274\240\346\240\207\346\214\211\344\270\213\346\214\211\351\222\256*/\n"
"QToolBox::tab:pressed\n"
"{\n"
"    background-color: gray; \n"
"    color:white; \n"
"    border:2px solid gray;\n"
"    border-radius:10px;\n"
"}\n"
"\n"
"\n"
"\n"
"/*********/\n"
" \n"
"\n"
""));
        actionTriTransmit = new QAction(DynamicCalibration);
        actionTriTransmit->setObjectName(QString::fromUtf8("actionTriTransmit"));
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/share.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionTriTransmit->setIcon(icon);
        actionTriClear = new QAction(DynamicCalibration);
        actionTriClear->setObjectName(QString::fromUtf8("actionTriClear"));
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/delete.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionTriClear->setIcon(icon1);
        actionTofTransmit = new QAction(DynamicCalibration);
        actionTofTransmit->setObjectName(QString::fromUtf8("actionTofTransmit"));
        actionTofTransmit->setIcon(icon);
        actionTofClear = new QAction(DynamicCalibration);
        actionTofClear->setObjectName(QString::fromUtf8("actionTofClear"));
        actionTofClear->setIcon(icon1);
        actionImptParam = new QAction(DynamicCalibration);
        actionImptParam->setObjectName(QString::fromUtf8("actionImptParam"));
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/import.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionImptParam->setIcon(icon2);
        actionExptParam = new QAction(DynamicCalibration);
        actionExptParam->setObjectName(QString::fromUtf8("actionExptParam"));
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/download2.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionExptParam->setIcon(icon3);
        actioncompensationAngle = new QAction(DynamicCalibration);
        actioncompensationAngle->setObjectName(QString::fromUtf8("actioncompensationAngle"));
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/revert.png"), QSize(), QIcon::Normal, QIcon::Off);
        actioncompensationAngle->setIcon(icon4);
        actionSamplePoint = new QAction(DynamicCalibration);
        actionSamplePoint->setObjectName(QString::fromUtf8("actionSamplePoint"));
        QIcon icon5;
        icon5.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/scan5.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionSamplePoint->setIcon(icon5);
        actionCompareData = new QAction(DynamicCalibration);
        actionCompareData->setObjectName(QString::fromUtf8("actionCompareData"));
        QIcon icon6;
        icon6.addFile(QString::fromUtf8(":/new/prefix1/icon/logo/searchOnce.png"), QSize(), QIcon::Normal, QIcon::Off);
        actionCompareData->setIcon(icon6);
        centralwidget = new QWidget(DynamicCalibration);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        gridLayout_2 = new QGridLayout(centralwidget);
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        verticalLayout = new QVBoxLayout();
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        label = new QLabel(centralwidget);
        label->setObjectName(QString::fromUtf8("label"));
        label->setAlignment(Qt::AlignCenter);

        horizontalLayout->addWidget(label);

        comboBoxMode = new QComboBox(centralwidget);
        comboBoxMode->setObjectName(QString::fromUtf8("comboBoxMode"));
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(comboBoxMode->sizePolicy().hasHeightForWidth());
        comboBoxMode->setSizePolicy(sizePolicy);

        horizontalLayout->addWidget(comboBoxMode);


        verticalLayout->addLayout(horizontalLayout);

        tableWidget = new QTableWidget(centralwidget);
        if (tableWidget->columnCount() < 4)
            tableWidget->setColumnCount(4);
        if (tableWidget->rowCount() < 18)
            tableWidget->setRowCount(18);
        tableWidget->setObjectName(QString::fromUtf8("tableWidget"));
        sizePolicy.setHeightForWidth(tableWidget->sizePolicy().hasHeightForWidth());
        tableWidget->setSizePolicy(sizePolicy);
        tableWidget->setRowCount(18);
        tableWidget->setColumnCount(4);
        tableWidget->horizontalHeader()->setVisible(false);
        tableWidget->horizontalHeader()->setDefaultSectionSize(88);

        verticalLayout->addWidget(tableWidget);

        verticalLayout->setStretch(0, 1);
        verticalLayout->setStretch(1, 9);

        horizontalLayout_3->addLayout(verticalLayout);

        widget = new QCustomPlot(centralwidget);
        widget->setObjectName(QString::fromUtf8("widget"));

        horizontalLayout_3->addWidget(widget);

        horizontalLayout_3->setStretch(0, 2);
        horizontalLayout_3->setStretch(1, 3);

        verticalLayout_2->addLayout(horizontalLayout_3);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setSpacing(5);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        textEditDisLog = new QTextEdit(centralwidget);
        textEditDisLog->setObjectName(QString::fromUtf8("textEditDisLog"));
        sizePolicy.setHeightForWidth(textEditDisLog->sizePolicy().hasHeightForWidth());
        textEditDisLog->setSizePolicy(sizePolicy);

        horizontalLayout_2->addWidget(textEditDisLog);

        textEditPeakLog = new QTextEdit(centralwidget);
        textEditPeakLog->setObjectName(QString::fromUtf8("textEditPeakLog"));
        sizePolicy.setHeightForWidth(textEditPeakLog->sizePolicy().hasHeightForWidth());
        textEditPeakLog->setSizePolicy(sizePolicy);

        horizontalLayout_2->addWidget(textEditPeakLog);

        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        radioButton = new QRadioButton(centralwidget);
        radioButton->setObjectName(QString::fromUtf8("radioButton"));
        sizePolicy.setHeightForWidth(radioButton->sizePolicy().hasHeightForWidth());
        radioButton->setSizePolicy(sizePolicy);
        radioButton->setChecked(false);

        gridLayout->addWidget(radioButton, 0, 0, 1, 2);

        pushButtonStaticCalibration = new QPushButton(centralwidget);
        pushButtonStaticCalibration->setObjectName(QString::fromUtf8("pushButtonStaticCalibration"));
        sizePolicy.setHeightForWidth(pushButtonStaticCalibration->sizePolicy().hasHeightForWidth());
        pushButtonStaticCalibration->setSizePolicy(sizePolicy);

        gridLayout->addWidget(pushButtonStaticCalibration, 1, 0, 1, 1);

        pushButtonDynamicCalibration = new QPushButton(centralwidget);
        pushButtonDynamicCalibration->setObjectName(QString::fromUtf8("pushButtonDynamicCalibration"));
        sizePolicy.setHeightForWidth(pushButtonDynamicCalibration->sizePolicy().hasHeightForWidth());
        pushButtonDynamicCalibration->setSizePolicy(sizePolicy);

        gridLayout->addWidget(pushButtonDynamicCalibration, 1, 1, 1, 1);


        horizontalLayout_2->addLayout(gridLayout);


        verticalLayout_2->addLayout(horizontalLayout_2);

        verticalLayout_2->setStretch(0, 5);
        verticalLayout_2->setStretch(1, 2);

        gridLayout_2->addLayout(verticalLayout_2, 0, 0, 1, 1);

        DynamicCalibration->setCentralWidget(centralwidget);
        toolBar = new QToolBar(DynamicCalibration);
        toolBar->setObjectName(QString::fromUtf8("toolBar"));
        sizePolicy.setHeightForWidth(toolBar->sizePolicy().hasHeightForWidth());
        toolBar->setSizePolicy(sizePolicy);
        toolBar->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);
        DynamicCalibration->addToolBar(Qt::TopToolBarArea, toolBar);

        toolBar->addAction(actionTriTransmit);
        toolBar->addAction(actionTriClear);
        toolBar->addSeparator();
        toolBar->addAction(actionTofTransmit);
        toolBar->addAction(actionTofClear);
        toolBar->addSeparator();
        toolBar->addAction(actionImptParam);
        toolBar->addAction(actionExptParam);
        toolBar->addSeparator();
        toolBar->addAction(actioncompensationAngle);
        toolBar->addAction(actionSamplePoint);
        toolBar->addAction(actionCompareData);

        retranslateUi(DynamicCalibration);

        QMetaObject::connectSlotsByName(DynamicCalibration);
    } // setupUi

    void retranslateUi(QMainWindow *DynamicCalibration)
    {
        DynamicCalibration->setWindowTitle(QCoreApplication::translate("DynamicCalibration", "MainWindow", nullptr));
        actionTriTransmit->setText(QCoreApplication::translate("DynamicCalibration", "TriTransmit", nullptr));
#if QT_CONFIG(tooltip)
        actionTriTransmit->setToolTip(QCoreApplication::translate("DynamicCalibration", "\344\270\211\350\247\222\346\263\225\345\217\202\346\225\260\345\217\221\351\200\201", nullptr));
#endif // QT_CONFIG(tooltip)
        actionTriClear->setText(QCoreApplication::translate("DynamicCalibration", "TriClear", nullptr));
#if QT_CONFIG(tooltip)
        actionTriClear->setToolTip(QCoreApplication::translate("DynamicCalibration", "\344\270\211\350\247\222\346\263\225\345\217\202\346\225\260\346\270\205\351\231\244", nullptr));
#endif // QT_CONFIG(tooltip)
        actionTofTransmit->setText(QCoreApplication::translate("DynamicCalibration", "TofTransmit", nullptr));
#if QT_CONFIG(tooltip)
        actionTofTransmit->setToolTip(QCoreApplication::translate("DynamicCalibration", "tof\345\217\202\346\225\260\345\217\221\351\200\201", nullptr));
#endif // QT_CONFIG(tooltip)
        actionTofClear->setText(QCoreApplication::translate("DynamicCalibration", "TofClear", nullptr));
#if QT_CONFIG(tooltip)
        actionTofClear->setToolTip(QCoreApplication::translate("DynamicCalibration", "tof\345\217\202\346\225\260\346\270\205\351\231\244", nullptr));
#endif // QT_CONFIG(tooltip)
        actionImptParam->setText(QCoreApplication::translate("DynamicCalibration", "ImptParam", nullptr));
#if QT_CONFIG(tooltip)
        actionImptParam->setToolTip(QCoreApplication::translate("DynamicCalibration", "\345\257\274\345\205\245\346\240\241\346\255\243\345\217\202\346\225\260", nullptr));
#endif // QT_CONFIG(tooltip)
        actionExptParam->setText(QCoreApplication::translate("DynamicCalibration", "ExptParam", nullptr));
#if QT_CONFIG(tooltip)
        actionExptParam->setToolTip(QCoreApplication::translate("DynamicCalibration", "\345\257\274\345\207\272\345\217\202\346\225\260", nullptr));
#endif // QT_CONFIG(tooltip)
        actioncompensationAngle->setText(QCoreApplication::translate("DynamicCalibration", "compensationAngle", nullptr));
#if QT_CONFIG(tooltip)
        actioncompensationAngle->setToolTip(QCoreApplication::translate("DynamicCalibration", "\350\241\245\345\201\277\351\233\266\345\272\246\350\247\222", nullptr));
#endif // QT_CONFIG(tooltip)
        actionSamplePoint->setText(QCoreApplication::translate("DynamicCalibration", "SampleData", nullptr));
#if QT_CONFIG(tooltip)
        actionSamplePoint->setToolTip(QCoreApplication::translate("DynamicCalibration", "\345\274\200\345\247\213\351\207\207\351\233\206\346\225\260\346\215\256", nullptr));
#endif // QT_CONFIG(tooltip)
        actionCompareData->setText(QCoreApplication::translate("DynamicCalibration", "CompareData", nullptr));
#if QT_CONFIG(tooltip)
        actionCompareData->setToolTip(QCoreApplication::translate("DynamicCalibration", "\351\207\207\351\233\206\346\225\260\346\215\256\345\271\266\346\257\224\345\257\271", nullptr));
#endif // QT_CONFIG(tooltip)
        label->setText(QCoreApplication::translate("DynamicCalibration", "CalibrationMode", nullptr));
        radioButton->setText(QCoreApplication::translate("DynamicCalibration", "Rolling Sample Data", nullptr));
        pushButtonStaticCalibration->setText(QCoreApplication::translate("DynamicCalibration", "StaticCalibration", nullptr));
        pushButtonDynamicCalibration->setText(QCoreApplication::translate("DynamicCalibration", "DynamicCalibration", nullptr));
        toolBar->setWindowTitle(QCoreApplication::translate("DynamicCalibration", "toolBar", nullptr));
    } // retranslateUi

};

namespace Ui {
    class DynamicCalibration: public Ui_DynamicCalibration {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_DYNAMICCALIBRATION_H
