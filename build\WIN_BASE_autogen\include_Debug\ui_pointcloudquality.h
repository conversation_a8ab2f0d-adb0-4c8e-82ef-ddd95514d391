/********************************************************************************
** Form generated from reading UI file 'pointcloudquality.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_POINTCLOUDQUALITY_H
#define UI_POINTCLOUDQUALITY_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPlainTextEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "qcustomplot.h"

QT_BEGIN_NAMESPACE

class Ui_PointCloudQuality
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout_2;
    QHBoxLayout *horizontalLayout;
    QWidget *widget;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_2;
    QLabel *label;
    QLineEdit *lineEdit_StandardAngle;
    QSpacerItem *horizontalSpacer_2;
    QHBoxLayout *horizontalLayout_3;
    QLabel *label_8;
    QLineEdit *lineEdit_StandardDistance;
    QSpacerItem *horizontalSpacer_3;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_2;
    QLineEdit *lineEdit_GlobalAngleL;
    QLineEdit *lineEdit_GlobalAngleR;
    QHBoxLayout *horizontalLayout_5;
    QLabel *label_7;
    QLineEdit *lineEdit_SampleNum;
    QSpacerItem *horizontalSpacer;
    QHBoxLayout *horizontalLayout_6;
    QLabel *label_3;
    QLineEdit *lineEdit_Threshold;
    QSpacerItem *horizontalSpacer_4;
    QHBoxLayout *horizontalLayout_7;
    QLabel *label_5;
    QLineEdit *lineEdit_DriftAngle;
    QSpacerItem *horizontalSpacer_5;
    QHBoxLayout *horizontalLayout_8;
    QLabel *label_6;
    QLineEdit *lineEdit_OutlierRate;
    QLabel *label_4;
    QLineEdit *lineEdit_OutlierControlDistance;
    QHBoxLayout *horizontalLayout_9;
    QLabel *label_9;
    QComboBox *comboBox_Material;
    QLabel *label_13;
    QSpacerItem *horizontalSpacer_6;
    QHBoxLayout *horizontalLayout_10;
    QLabel *label_10;
    QLineEdit *lineEdit_BreakAngle;
    QSpacerItem *horizontalSpacer_7;
    QHBoxLayout *horizontalLayout_11;
    QLabel *label_11;
    QLineEdit *lineEdit_BreakAngle_1_RangeL;
    QLineEdit *lineEdit_BreakAngle_1_RangeR;
    QHBoxLayout *horizontalLayout_12;
    QLabel *label_12;
    QLineEdit *lineEdit_BreakAngle_2_RangeL;
    QLineEdit *lineEdit_BreakAngle_2_RangeL_2;
    QHBoxLayout *horizontalLayout_13;
    QPushButton *btn_Cal;
    QPlainTextEdit *plainTextEdit;
    QCustomPlot *plot;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *PointCloudQuality)
    {
        if (PointCloudQuality->objectName().isEmpty())
            PointCloudQuality->setObjectName(QString::fromUtf8("PointCloudQuality"));
        PointCloudQuality->resize(1208, 747);
        centralwidget = new QWidget(PointCloudQuality);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        verticalLayout_2 = new QVBoxLayout(centralwidget);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        widget = new QWidget(centralwidget);
        widget->setObjectName(QString::fromUtf8("widget"));
        verticalLayout = new QVBoxLayout(widget);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        label = new QLabel(widget);
        label->setObjectName(QString::fromUtf8("label"));

        horizontalLayout_2->addWidget(label);

        lineEdit_StandardAngle = new QLineEdit(widget);
        lineEdit_StandardAngle->setObjectName(QString::fromUtf8("lineEdit_StandardAngle"));
        lineEdit_StandardAngle->setAlignment(Qt::AlignCenter);

        horizontalLayout_2->addWidget(lineEdit_StandardAngle);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_2);


        verticalLayout->addLayout(horizontalLayout_2);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        label_8 = new QLabel(widget);
        label_8->setObjectName(QString::fromUtf8("label_8"));

        horizontalLayout_3->addWidget(label_8);

        lineEdit_StandardDistance = new QLineEdit(widget);
        lineEdit_StandardDistance->setObjectName(QString::fromUtf8("lineEdit_StandardDistance"));
        lineEdit_StandardDistance->setAlignment(Qt::AlignCenter);

        horizontalLayout_3->addWidget(lineEdit_StandardDistance);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_3);


        verticalLayout->addLayout(horizontalLayout_3);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        label_2 = new QLabel(widget);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        horizontalLayout_4->addWidget(label_2);

        lineEdit_GlobalAngleL = new QLineEdit(widget);
        lineEdit_GlobalAngleL->setObjectName(QString::fromUtf8("lineEdit_GlobalAngleL"));
        lineEdit_GlobalAngleL->setAlignment(Qt::AlignCenter);

        horizontalLayout_4->addWidget(lineEdit_GlobalAngleL);

        lineEdit_GlobalAngleR = new QLineEdit(widget);
        lineEdit_GlobalAngleR->setObjectName(QString::fromUtf8("lineEdit_GlobalAngleR"));
        lineEdit_GlobalAngleR->setAlignment(Qt::AlignCenter);

        horizontalLayout_4->addWidget(lineEdit_GlobalAngleR);


        verticalLayout->addLayout(horizontalLayout_4);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        label_7 = new QLabel(widget);
        label_7->setObjectName(QString::fromUtf8("label_7"));

        horizontalLayout_5->addWidget(label_7);

        lineEdit_SampleNum = new QLineEdit(widget);
        lineEdit_SampleNum->setObjectName(QString::fromUtf8("lineEdit_SampleNum"));
        lineEdit_SampleNum->setAlignment(Qt::AlignCenter);

        horizontalLayout_5->addWidget(lineEdit_SampleNum);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer);


        verticalLayout->addLayout(horizontalLayout_5);

        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        label_3 = new QLabel(widget);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        horizontalLayout_6->addWidget(label_3);

        lineEdit_Threshold = new QLineEdit(widget);
        lineEdit_Threshold->setObjectName(QString::fromUtf8("lineEdit_Threshold"));
        lineEdit_Threshold->setAlignment(Qt::AlignCenter);

        horizontalLayout_6->addWidget(lineEdit_Threshold);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_4);


        verticalLayout->addLayout(horizontalLayout_6);

        horizontalLayout_7 = new QHBoxLayout();
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        label_5 = new QLabel(widget);
        label_5->setObjectName(QString::fromUtf8("label_5"));

        horizontalLayout_7->addWidget(label_5);

        lineEdit_DriftAngle = new QLineEdit(widget);
        lineEdit_DriftAngle->setObjectName(QString::fromUtf8("lineEdit_DriftAngle"));
        lineEdit_DriftAngle->setAlignment(Qt::AlignCenter);

        horizontalLayout_7->addWidget(lineEdit_DriftAngle);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_5);


        verticalLayout->addLayout(horizontalLayout_7);

        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        label_6 = new QLabel(widget);
        label_6->setObjectName(QString::fromUtf8("label_6"));

        horizontalLayout_8->addWidget(label_6);

        lineEdit_OutlierRate = new QLineEdit(widget);
        lineEdit_OutlierRate->setObjectName(QString::fromUtf8("lineEdit_OutlierRate"));
        lineEdit_OutlierRate->setAlignment(Qt::AlignCenter);

        horizontalLayout_8->addWidget(lineEdit_OutlierRate);

        label_4 = new QLabel(widget);
        label_4->setObjectName(QString::fromUtf8("label_4"));

        horizontalLayout_8->addWidget(label_4);

        lineEdit_OutlierControlDistance = new QLineEdit(widget);
        lineEdit_OutlierControlDistance->setObjectName(QString::fromUtf8("lineEdit_OutlierControlDistance"));
        lineEdit_OutlierControlDistance->setAlignment(Qt::AlignCenter);

        horizontalLayout_8->addWidget(lineEdit_OutlierControlDistance);


        verticalLayout->addLayout(horizontalLayout_8);

        horizontalLayout_9 = new QHBoxLayout();
        horizontalLayout_9->setObjectName(QString::fromUtf8("horizontalLayout_9"));
        label_9 = new QLabel(widget);
        label_9->setObjectName(QString::fromUtf8("label_9"));

        horizontalLayout_9->addWidget(label_9);

        comboBox_Material = new QComboBox(widget);
        comboBox_Material->addItem(QString());
        comboBox_Material->addItem(QString());
        comboBox_Material->setObjectName(QString::fromUtf8("comboBox_Material"));

        horizontalLayout_9->addWidget(comboBox_Material);

        label_13 = new QLabel(widget);
        label_13->setObjectName(QString::fromUtf8("label_13"));

        horizontalLayout_9->addWidget(label_13);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_9->addItem(horizontalSpacer_6);


        verticalLayout->addLayout(horizontalLayout_9);

        horizontalLayout_10 = new QHBoxLayout();
        horizontalLayout_10->setObjectName(QString::fromUtf8("horizontalLayout_10"));
        label_10 = new QLabel(widget);
        label_10->setObjectName(QString::fromUtf8("label_10"));

        horizontalLayout_10->addWidget(label_10);

        lineEdit_BreakAngle = new QLineEdit(widget);
        lineEdit_BreakAngle->setObjectName(QString::fromUtf8("lineEdit_BreakAngle"));
        lineEdit_BreakAngle->setAlignment(Qt::AlignCenter);

        horizontalLayout_10->addWidget(lineEdit_BreakAngle);

        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_10->addItem(horizontalSpacer_7);


        verticalLayout->addLayout(horizontalLayout_10);

        horizontalLayout_11 = new QHBoxLayout();
        horizontalLayout_11->setObjectName(QString::fromUtf8("horizontalLayout_11"));
        label_11 = new QLabel(widget);
        label_11->setObjectName(QString::fromUtf8("label_11"));

        horizontalLayout_11->addWidget(label_11);

        lineEdit_BreakAngle_1_RangeL = new QLineEdit(widget);
        lineEdit_BreakAngle_1_RangeL->setObjectName(QString::fromUtf8("lineEdit_BreakAngle_1_RangeL"));
        lineEdit_BreakAngle_1_RangeL->setAlignment(Qt::AlignCenter);

        horizontalLayout_11->addWidget(lineEdit_BreakAngle_1_RangeL);

        lineEdit_BreakAngle_1_RangeR = new QLineEdit(widget);
        lineEdit_BreakAngle_1_RangeR->setObjectName(QString::fromUtf8("lineEdit_BreakAngle_1_RangeR"));
        lineEdit_BreakAngle_1_RangeR->setAlignment(Qt::AlignCenter);

        horizontalLayout_11->addWidget(lineEdit_BreakAngle_1_RangeR);


        verticalLayout->addLayout(horizontalLayout_11);

        horizontalLayout_12 = new QHBoxLayout();
        horizontalLayout_12->setObjectName(QString::fromUtf8("horizontalLayout_12"));
        label_12 = new QLabel(widget);
        label_12->setObjectName(QString::fromUtf8("label_12"));

        horizontalLayout_12->addWidget(label_12);

        lineEdit_BreakAngle_2_RangeL = new QLineEdit(widget);
        lineEdit_BreakAngle_2_RangeL->setObjectName(QString::fromUtf8("lineEdit_BreakAngle_2_RangeL"));
        lineEdit_BreakAngle_2_RangeL->setAlignment(Qt::AlignCenter);

        horizontalLayout_12->addWidget(lineEdit_BreakAngle_2_RangeL);

        lineEdit_BreakAngle_2_RangeL_2 = new QLineEdit(widget);
        lineEdit_BreakAngle_2_RangeL_2->setObjectName(QString::fromUtf8("lineEdit_BreakAngle_2_RangeL_2"));
        lineEdit_BreakAngle_2_RangeL_2->setAlignment(Qt::AlignCenter);

        horizontalLayout_12->addWidget(lineEdit_BreakAngle_2_RangeL_2);


        verticalLayout->addLayout(horizontalLayout_12);

        horizontalLayout_13 = new QHBoxLayout();
        horizontalLayout_13->setObjectName(QString::fromUtf8("horizontalLayout_13"));
        btn_Cal = new QPushButton(widget);
        btn_Cal->setObjectName(QString::fromUtf8("btn_Cal"));

        horizontalLayout_13->addWidget(btn_Cal);


        verticalLayout->addLayout(horizontalLayout_13);

        plainTextEdit = new QPlainTextEdit(widget);
        plainTextEdit->setObjectName(QString::fromUtf8("plainTextEdit"));

        verticalLayout->addWidget(plainTextEdit);


        horizontalLayout->addWidget(widget);

        plot = new QCustomPlot(centralwidget);
        plot->setObjectName(QString::fromUtf8("plot"));

        horizontalLayout->addWidget(plot);

        horizontalLayout->setStretch(0, 2);
        horizontalLayout->setStretch(1, 4);

        verticalLayout_2->addLayout(horizontalLayout);

        PointCloudQuality->setCentralWidget(centralwidget);
        statusbar = new QStatusBar(PointCloudQuality);
        statusbar->setObjectName(QString::fromUtf8("statusbar"));
        PointCloudQuality->setStatusBar(statusbar);

        retranslateUi(PointCloudQuality);

        QMetaObject::connectSlotsByName(PointCloudQuality);
    } // setupUi

    void retranslateUi(QMainWindow *PointCloudQuality)
    {
        PointCloudQuality->setWindowTitle(QCoreApplication::translate("PointCloudQuality", "MainWindow", nullptr));
        label->setText(QCoreApplication::translate("PointCloudQuality", "\346\265\213\350\257\225\346\214\241\346\235\277\346\260\264\345\271\263\350\247\222\345\272\246(\345\215\225\344\275\215\357\274\232\302\260)\357\274\232", nullptr));
        lineEdit_StandardAngle->setText(QCoreApplication::translate("PointCloudQuality", "90", nullptr));
        label_8->setText(QCoreApplication::translate("PointCloudQuality", "\346\265\213\350\257\225\346\214\241\346\235\277\350\267\235\347\246\273(\345\215\225\344\275\215\357\274\232mm):", nullptr));
        lineEdit_StandardDistance->setText(QCoreApplication::translate("PointCloudQuality", "400", nullptr));
        label_2->setText(QCoreApplication::translate("PointCloudQuality", "\346\265\213\350\257\225\350\247\222\345\272\246\350\214\203\345\233\264(\345\215\225\344\275\215\357\274\232\302\260)\357\274\232", nullptr));
        lineEdit_GlobalAngleL->setText(QCoreApplication::translate("PointCloudQuality", "-30.6", nullptr));
        lineEdit_GlobalAngleR->setText(QCoreApplication::translate("PointCloudQuality", "30.6", nullptr));
        label_7->setText(QCoreApplication::translate("PointCloudQuality", "\351\207\207\346\240\267\345\270\247\346\225\260\357\274\232", nullptr));
        lineEdit_SampleNum->setText(QCoreApplication::translate("PointCloudQuality", "10", nullptr));
        label_3->setText(QCoreApplication::translate("PointCloudQuality", "\351\230\210\345\200\274\342\211\244(\345\215\225\344\275\215\357\274\232mm)\357\274\232", nullptr));
        lineEdit_Threshold->setText(QCoreApplication::translate("PointCloudQuality", "8", nullptr));
        label_5->setText(QCoreApplication::translate("PointCloudQuality", "\345\201\217\350\247\222\342\211\244(\345\215\225\344\275\215\357\274\232\302\260):", nullptr));
        lineEdit_DriftAngle->setText(QCoreApplication::translate("PointCloudQuality", "0.9", nullptr));
        label_6->setText(QCoreApplication::translate("PointCloudQuality", "\345\244\226\347\202\271\347\216\207\342\211\244(\345\215\225\344\275\215\357\274\232%):", nullptr));
        lineEdit_OutlierRate->setText(QCoreApplication::translate("PointCloudQuality", "4", nullptr));
        label_4->setText(QCoreApplication::translate("PointCloudQuality", "\345\215\241\346\216\247\342\211\244(\345\215\225\344\275\215\357\274\232mm):", nullptr));
        lineEdit_OutlierControlDistance->setText(QCoreApplication::translate("PointCloudQuality", "6", nullptr));
        label_9->setText(QCoreApplication::translate("PointCloudQuality", "\346\235\220\350\264\250\357\274\232", nullptr));
        comboBox_Material->setItemText(0, QCoreApplication::translate("PointCloudQuality", "\347\231\275\345\215\241", nullptr));
        comboBox_Material->setItemText(1, QCoreApplication::translate("PointCloudQuality", "\346\234\250\347\272\271\346\235\277", nullptr));

        label_13->setText(QCoreApplication::translate("PointCloudQuality", "\346\263\250\357\274\232\346\234\250\347\272\271\346\235\277\346\211\215\346\234\211\350\256\241\347\256\227\346\212\230\350\247\222", nullptr));
        label_10->setText(QCoreApplication::translate("PointCloudQuality", "\346\212\230\350\247\222\342\211\244(\345\215\225\344\275\215\357\274\232\302\260):", nullptr));
        lineEdit_BreakAngle->setText(QCoreApplication::translate("PointCloudQuality", "1.4", nullptr));
        label_11->setText(QCoreApplication::translate("PointCloudQuality", "\346\212\230\350\247\222\347\272\2771\350\247\222\345\272\246\350\214\203\345\233\264(\345\215\225\344\275\215\357\274\232\302\260)\357\274\232", nullptr));
        lineEdit_BreakAngle_1_RangeL->setText(QCoreApplication::translate("PointCloudQuality", "20", nullptr));
        lineEdit_BreakAngle_1_RangeR->setText(QCoreApplication::translate("PointCloudQuality", "50", nullptr));
        label_12->setText(QCoreApplication::translate("PointCloudQuality", "\346\212\230\350\247\222\347\272\2772\350\247\222\345\272\246\350\214\203\345\233\264(\345\215\225\344\275\215\357\274\232\302\260)\357\274\232", nullptr));
        lineEdit_BreakAngle_2_RangeL->setText(QCoreApplication::translate("PointCloudQuality", "130", nullptr));
        lineEdit_BreakAngle_2_RangeL_2->setText(QCoreApplication::translate("PointCloudQuality", "160", nullptr));
        btn_Cal->setText(QCoreApplication::translate("PointCloudQuality", "\350\256\241\347\256\227", nullptr));
    } // retranslateUi

};

namespace Ui {
    class PointCloudQuality: public Ui_PointCloudQuality {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_POINTCLOUDQUALITY_H
