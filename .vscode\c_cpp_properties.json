{
    "configurations": [
        {
            // "name": "Win32",
            // "cStandard": "c11",
            // "cppStandard": "gnu++14",
            // "intelliSenseMode": "gcc-x64",
            // "configurationProvider": "ms-vscode.cmake-tools"
            "name": "Qt App",
            // "compilerPath": "/usr/bin/gcc",
            "includePath": [
                "${workspaceFolder}/**",
                "D:/Programs/Qt/6.8.3/mingw_64\\include",
                "D:/Programs/Qt/6.8.3/mingw_64\\include\\QtGui",
                "D:/Programs/Qt/6.8.3/mingw_64\\include\\QtCore",
                "D:/Programs/Qt/6.8.3/mingw_64\\include\\QtWidgets",
                "D:/Programs/Qt/6.8.3/mingw_64\\include\\QtDesigner",
                "D:/Programs/Qt/6.8.3/mingw_64\\include\\QtCharts",
                "D:/Programs/Qt/6.8.3/mingw_64\\include\\QtSql",
                "D:/Programs/Qt/6.8.3/mingw_64\\include\\QtSerialPort",
                "D:/Programs/Qt/6.8.3/mingw_64\\include\\QtOpenGL",
                "D:/Programs/Qt/6.8.3/mingw_64\\include\\QtQuick"
            ],
            "defines": [
                "WIN32",
                "_DEBUG",
                "UNICODE",
                "_UNICODE",
                "QT_WIDGETS_LIB",
                "QT_GUI_LIB",
                "QT_CORE_LIB"
            ],
            "compileCommands": "${workspaceFolder}\\build\\compile_commands.json", //��cmake tool�Զ�����
            "cStandard": "c17",
            "cppStandard": "gnu++20",
            "intelliSenseMode": "windows-gcc-x64",
            "configurationProvider": "ms-vscode.cmake-tools"
        }
    ],
    "version": 4
}