{
    "configurations": [
        {
            // "name": "Win32",
            // "cStandard": "c11",
            // "cppStandard": "gnu++14",
            // "intelliSenseMode": "gcc-x64",
            // "configurationProvider": "ms-vscode.cmake-tools"
            "name": "Qt App",
            // "compilerPath": "/usr/bin/gcc",
            "includePath": [
                "${workspaceFolder}/**",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtGui",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtCore",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtWidgets",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtDesigner",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtCharts",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtSql",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtSerialPort",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtOpenGL",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtQuick",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtAxContainer",
                "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64\\include\\QtPrintSupport"
            ],
            "defines": [
                "WIN32",
                "_DEBUG",
                "UNICODE",
                "_UNICODE",
                "QT_WIDGETS_LIB",
                "QT_GUI_LIB",
                "QT_CORE_LIB"
            ],
            "compileCommands": "${workspaceFolder}\\build\\compile_commands.json", //��cmake tool�Զ�����
            "cStandard": "c17",
            "cppStandard": "gnu++20",
            "intelliSenseMode": "windows-gcc-x64",
            "configurationProvider": "ms-vscode.cmake-tools"
        }
    ],
    "version": 4
}