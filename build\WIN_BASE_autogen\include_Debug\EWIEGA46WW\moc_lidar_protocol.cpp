/****************************************************************************
** Meta object code from reading C++ file 'lidar_protocol.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../lidar_protocol.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'lidar_protocol.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_lidardevice__ProcessProtocol_t {
    QByteArrayData data[25];
    char stringdata0[330];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_lidardevice__ProcessProtocol_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_lidardevice__ProcessProtocol_t qt_meta_stringdata_lidardevice__ProcessProtocol = {
    {
QT_MOC_LITERAL(0, 0, 28), // "lidardevice::ProcessProtocol"
QT_MOC_LITERAL(1, 29, 27), // "ProcessSendToMainPointCloud"
QT_MOC_LITERAL(2, 57, 0), // ""
QT_MOC_LITERAL(3, 58, 25), // "std::vector<ProtocolData>"
QT_MOC_LITERAL(4, 84, 4), // "data"
QT_MOC_LITERAL(5, 89, 12), // "FeedbackInfo"
QT_MOC_LITERAL(6, 102, 3), // "fdb"
QT_MOC_LITERAL(7, 106, 21), // "transmitHistogramData"
QT_MOC_LITERAL(8, 128, 2), // "tr"
QT_MOC_LITERAL(9, 131, 19), // "transmitGreymapData"
QT_MOC_LITERAL(10, 151, 23), // "transmitCalibrationData"
QT_MOC_LITERAL(11, 175, 22), // "transmitPointCloudData"
QT_MOC_LITERAL(12, 198, 19), // "TransmitTemperature"
QT_MOC_LITERAL(13, 218, 4), // "temp"
QT_MOC_LITERAL(14, 223, 11), // "sig_iap_ack"
QT_MOC_LITERAL(15, 235, 9), // "iap_ack_t"
QT_MOC_LITERAL(16, 245, 3), // "ack"
QT_MOC_LITERAL(17, 249, 10), // "QuitThread"
QT_MOC_LITERAL(18, 260, 7), // "isClose"
QT_MOC_LITERAL(19, 268, 12), // "TransmitData"
QT_MOC_LITERAL(20, 281, 3), // "str"
QT_MOC_LITERAL(21, 285, 15), // "SetProtocolType"
QT_MOC_LITERAL(22, 301, 12), // "protocolType"
QT_MOC_LITERAL(23, 314, 10), // "RecordData"
QT_MOC_LITERAL(24, 325, 4) // "isOk"

    },
    "lidardevice::ProcessProtocol\0"
    "ProcessSendToMainPointCloud\0\0"
    "std::vector<ProtocolData>\0data\0"
    "FeedbackInfo\0fdb\0transmitHistogramData\0"
    "tr\0transmitGreymapData\0transmitCalibrationData\0"
    "transmitPointCloudData\0TransmitTemperature\0"
    "temp\0sig_iap_ack\0iap_ack_t\0ack\0"
    "QuitThread\0isClose\0TransmitData\0str\0"
    "SetProtocolType\0protocolType\0RecordData\0"
    "isOk"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_lidardevice__ProcessProtocol[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   74,    2, 0x06 /* Public */,
       5,    1,   77,    2, 0x06 /* Public */,
       7,    1,   80,    2, 0x06 /* Public */,
       9,    1,   83,    2, 0x06 /* Public */,
      10,    1,   86,    2, 0x06 /* Public */,
      11,    1,   89,    2, 0x06 /* Public */,
      12,    1,   92,    2, 0x06 /* Public */,
      14,    1,   95,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      17,    1,   98,    2, 0x0a /* Public */,
      19,    1,  101,    2, 0x0a /* Public */,
      21,    1,  104,    2, 0x0a /* Public */,
      23,    1,  107,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::QByteArray,    6,
    QMetaType::Void, QMetaType::QByteArray,    8,
    QMetaType::Void, QMetaType::QByteArray,    8,
    QMetaType::Void, QMetaType::QByteArray,    8,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::Float,   13,
    QMetaType::Void, 0x80000000 | 15,   16,

 // slots: parameters
    QMetaType::Void, QMetaType::Bool,   18,
    QMetaType::Void, QMetaType::QByteArray,   20,
    QMetaType::Void, QMetaType::Int,   22,
    QMetaType::Void, QMetaType::Bool,   24,

       0        // eod
};

void lidardevice::ProcessProtocol::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ProcessProtocol *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->ProcessSendToMainPointCloud((*reinterpret_cast< std::vector<ProtocolData>(*)>(_a[1]))); break;
        case 1: _t->FeedbackInfo((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 2: _t->transmitHistogramData((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 3: _t->transmitGreymapData((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 4: _t->transmitCalibrationData((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 5: _t->transmitPointCloudData((*reinterpret_cast< std::vector<ProtocolData>(*)>(_a[1]))); break;
        case 6: _t->TransmitTemperature((*reinterpret_cast< float(*)>(_a[1]))); break;
        case 7: _t->sig_iap_ack((*reinterpret_cast< iap_ack_t(*)>(_a[1]))); break;
        case 8: _t->QuitThread((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 9: _t->TransmitData((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 10: _t->SetProtocolType((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 11: _t->RecordData((*reinterpret_cast< bool(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ProcessProtocol::*)(std::vector<ProtocolData> );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::ProcessSendToMainPointCloud)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::FeedbackInfo)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::transmitHistogramData)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::transmitGreymapData)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(QByteArray );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::transmitCalibrationData)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(std::vector<ProtocolData> );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::transmitPointCloudData)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(float );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::TransmitTemperature)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (ProcessProtocol::*)(iap_ack_t );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProcessProtocol::sig_iap_ack)) {
                *result = 7;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject lidardevice::ProcessProtocol::staticMetaObject = { {
    QMetaObject::SuperData::link<SerialBase::staticMetaObject>(),
    qt_meta_stringdata_lidardevice__ProcessProtocol.data,
    qt_meta_data_lidardevice__ProcessProtocol,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *lidardevice::ProcessProtocol::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *lidardevice::ProcessProtocol::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_lidardevice__ProcessProtocol.stringdata0))
        return static_cast<void*>(this);
    return SerialBase::qt_metacast(_clname);
}

int lidardevice::ProcessProtocol::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = SerialBase::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void lidardevice::ProcessProtocol::ProcessSendToMainPointCloud(std::vector<ProtocolData> _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void lidardevice::ProcessProtocol::FeedbackInfo(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void lidardevice::ProcessProtocol::transmitHistogramData(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void lidardevice::ProcessProtocol::transmitGreymapData(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void lidardevice::ProcessProtocol::transmitCalibrationData(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void lidardevice::ProcessProtocol::transmitPointCloudData(std::vector<ProtocolData> _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void lidardevice::ProcessProtocol::TransmitTemperature(float _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void lidardevice::ProcessProtocol::sig_iap_ack(iap_ack_t _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
