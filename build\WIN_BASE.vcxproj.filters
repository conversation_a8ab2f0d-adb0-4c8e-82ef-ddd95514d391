﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\widget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dynamic_calibration.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\import_pointcloud.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloud_quality.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\show_image.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidar_protocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\other_protocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\record_pointcloud.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\saveas_capture.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\serial_base.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\calibration_chart.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\greymap_chart.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\histogram_chart.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointclouds_chart.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidar_database.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\task_process\task_base.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_logo2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\EWIEGA46WW\qrc_res.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\widget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dynamic_calibration.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\import_pointcloud.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidar_protocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\other_protocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloud_quality.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\record_pointcloud.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\saveas_capture.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\serial_base.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\calibration_chart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\greymap_chart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\histogram_chart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointclouds_chart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidar_database.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\show_image.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\task_process\task_base.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dtof_calibration.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_widget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_dynamiccalibration.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_pointcloudquality.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_showimage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_importpointcloud.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_recordpointcloud.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_saveascapture.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_calibrationchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_greymapchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_histogramchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_pointcloudschart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Debug\ui_lidardatabase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_widget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_dynamiccalibration.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_pointcloudquality.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_showimage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_importpointcloud.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_recordpointcloud.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_saveascapture.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_calibrationchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_greymapchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_histogramchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_pointcloudschart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_Release\ui_lidardatabase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_widget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_dynamiccalibration.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_pointcloudquality.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_showimage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_importpointcloud.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_recordpointcloud.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_saveascapture.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_calibrationchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_greymapchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_histogramchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_pointcloudschart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_MinSizeRel\ui_lidardatabase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_widget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_dynamiccalibration.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_pointcloudquality.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_showimage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_importpointcloud.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_recordpointcloud.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_saveascapture.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_calibrationchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_greymapchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_histogramchart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_pointcloudschart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\include_RelWithDebInfo\ui_lidardatabase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\b757634ceedbf6ab2645d3fff98d5cb3\autouic_(CONFIG).stamp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\2f1de9a012f3b2153bcbe432347b727f\qrc_logo.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\2f1de9a012f3b2153bcbe432347b727f\qrc_logo2.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\CMakeFiles\2f1de9a012f3b2153bcbe432347b727f\qrc_res.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\widget.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\dynamiccalibration.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudquality.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\showimage.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\importpointcloud.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\recordpointcloud.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\saveascapture.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\calibrationchart.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\greymapchart.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\histogramchart.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\pointcloudschart.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\lidardatabase.ui" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo.qrc" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\logo2.qrc" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\res.qrc" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_Debug.stamp" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_Release.stamp" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_MinSizeRel.stamp" />
    <None Include="F:\13_Yapha-Laser-DTof2dMS\development\tool\winbase_app_0908\build\WIN_BASE_autogen\autouic_RelWithDebInfo.stamp" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{E3EC73A3-B92A-3921-B236-30BFC4F5A5D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{E12AADDA-306D-3673-8D68-E13A6BC60B36}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{D9DA7051-E70D-325B-9351-89FA517C9A8D}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
